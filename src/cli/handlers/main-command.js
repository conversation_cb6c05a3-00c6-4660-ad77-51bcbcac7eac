/**
 * 主命令处理器
 * @description 处理Claude Code的主要交互命令
 * @original 原始代码在57287-57630行 (主命令的action函数)
 */

import { validateUuid, isSessionIdInUse } from '../../utils/validation.js';
import { resolveFilePath, fileExists, readFileSync } from '../../utils/file-system.js';
import { parseJsonSafely } from '../../utils/json.js';
import { setupAuthentication } from '../../services/auth-service.js';
import { initializeWorkspace } from '../../services/workspace-service.js';
import { startInteractiveSession } from '../../services/session-service.js';
import { startPrintModeSession } from '../../services/print-mode-service.js';

/**
 * 处理主命令执行
 * @param {string} prompt - 用户输入的提示
 * @param {Object} options - 命令行选项
 * @returns {Promise<void>}
 * @original .action(async (D, Z) => { ... })
 */
export async function handleMainCommand(prompt, options) {
  const {
    debug = false,
    debugToStderr = false,
    verbose = false,
    print: printMode,
    dangerouslySkipPermissions,
    allowedTools = [],
    disallowedTools = [],
    mcpConfig,
    outputFormat,
    inputFormat,
    permissionMode,
    addDir = [],
    fallbackModel,
    ide = false,
    sessionId,
    settings,
    strictMcpConfig = false,
    teleport = null,
    remote = null,
    model,
    systemPrompt,
    systemPromptFile,
    appendSystemPrompt,
    continue: continueSession,
    resume,
    maxTurns,
    permissionPromptTool
  } = options;

  try {
    // 处理设置文件或JSON字符串
    if (settings) {
      await processSettingsOption(settings);
    }

    // 验证会话ID
    if (sessionId) {
      await validateSessionId(sessionId, options);
    }

    // 验证模型配置
    if (fallbackModel && model && fallbackModel === model) {
      console.error("Error: Fallback model cannot be the same as the main model. Please specify a different model for --fallback-model.");
      process.exit(1);
    }

    // 处理系统提示
    const finalSystemPrompt = await processSystemPrompt(systemPrompt, systemPromptFile);

    // 解析权限模式
    const permissionContext = parsePermissionMode({
      permissionModeCli: permissionMode,
      dangerouslySkipPermissions
    });

    // 处理MCP配置
    const dynamicMcpConfig = await processMcpConfig(mcpConfig);

    // 检查认证状态
    const isAuthenticated = await checkAuthenticationStatus();
    if (!isAuthenticated) {
      const authResult = await setupAuthentication(permissionContext);
      if (authResult && prompt?.trim().toLowerCase() === "/login") {
        prompt = "";
      }
      if (!authResult) {
        exitWithError();
      }
    }

    // 解析工具权限
    const { toolPermissionContext, warnings } = parseToolPermissions({
      allowedToolsCli: allowedTools,
      disallowedToolsCli: disallowedTools,
      permissionMode: permissionContext,
      addDirs: addDir
    });

    // 显示警告
    warnings.forEach(warning => {
      console.error(warning);
    });

    // 获取MCP服务器配置
    const mcpServers = strictMcpConfig ? {} : getAllMcpServers();
    const allMcpServers = {
      ...dynamicMcpConfig,
      ...mcpServers
    };

    // 验证输入格式
    validateInputFormat(inputFormat, outputFormat);

    // 处理输入
    const processedInput = await processInput(prompt || "", inputFormat ?? "text");

    // 初始化工作空间
    await initializeWorkspace(getCurrentWorkingDirectory(), permissionContext, printMode ?? false, false, sessionId ? validateUuid(sessionId) : undefined);

    // 获取工具和命令
    const [globalCommands, mcpResources] = await Promise.all([
      loadGlobalCommands(),
      processedInput || isAuthenticated ? initializeMcpResources(allMcpServers) : {
        clients: [],
        tools: [],
        commands: []
      }
    ]);

    // 记录遥测数据
    logTelemetry("tengu_init", {
      entrypoint: "claude",
      hasInitialPrompt: Boolean(prompt),
      hasStdin: Boolean(processedInput),
      verbose,
      debug,
      debugToStderr,
      print: printMode,
      outputFormat,
      inputFormat,
      numAllowedTools: allowedTools.length,
      numDisallowedTools: disallowedTools.length,
      mcpClientCount: Object.keys(getAllMcpServers()).length,
      worktree: false
    });

    // 启动会话
    if (isAuthenticated) {
      if (outputFormat === "stream-json" || outputFormat === "json") {
        enableJsonOutput(true);
      }

      await startPrintModeSession(processedInput, toolPermissionContext, mcpResources.clients, globalCommands, mcpResources.commands, [], mcpResources.tools, {
        continue: continueSession,
        resume,
        verbose,
        outputFormat,
        permissionPromptToolName: permissionPromptTool,
        allowedTools,
        maxTurns,
        systemPrompt: finalSystemPrompt,
        appendSystemPrompt,
        userSpecifiedModel: model,
        fallbackModel,
        teleport
      });
      return;
    }

    // 启动交互式会话
    await startInteractiveSession({
      prompt: processedInput,
      toolPermissionContext,
      mcpClients: mcpResources.clients,
      globalCommands,
      mcpCommands: mcpResources.commands,
      tools: mcpResources.tools,
      debug: debug || debugToStderr,
      verbose,
      continueSession,
      resume,
      model,
      fallbackModel,
      systemPrompt: finalSystemPrompt,
      appendSystemPrompt,
      autoConnectIde: ide,
      strictMcpConfig,
      dynamicMcpConfig,
      teleport,
      remote
    });

  } catch (error) {
    console.error("Error:", error.message);
    process.exit(1);
  }
}

/**
 * 处理设置选项
 * @param {string} settings - 设置文件路径或JSON字符串
 * @original if (O) try { ... }
 */
async function processSettingsOption(settings) {
  try {
    const trimmedSettings = settings.trim();
    const isJsonString = trimmedSettings.startsWith("{") && trimmedSettings.endsWith("}");
    let settingsPath;

    if (isJsonString) {
      if (!parseJsonSafely(trimmedSettings)) {
        console.error("Error: Invalid JSON provided to --settings");
        process.exit(1);
      }

      // 创建临时文件
      const { generateTempFilePath } = await import('../../utils/temp-file.js');
      const { writeFileSync } = await import("fs");
      
      settingsPath = generateTempFilePath("claude-settings", ".json");
      writeFileSync(settingsPath, trimmedSettings, "utf8");
    } else {
      const { resolvedPath } = resolveFilePath(process.cwd(), settings);
      if (!fileExists(resolvedPath)) {
        console.error(`Error: Settings file not found: ${resolvedPath}`);
        process.exit(1);
      }
      settingsPath = resolvedPath;
    }

    // 应用设置
    applySettings(settingsPath);
    refreshSettings();
  } catch (error) {
    console.error(`Error processing settings: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
}

/**
 * 验证会话ID
 * @param {string} sessionId - 会话ID
 * @param {Object} options - 命令选项
 * @original if (N) { ... }
 */
async function validateSessionId(sessionId, options) {
  if (options.continue || options.resume) {
    console.error("Error: --session-id cannot be used with --continue or --resume.");
    process.exit(1);
  }

  const validatedId = validateUuid(sessionId);
  if (!validatedId) {
    console.error("Error: Invalid session ID. Must be a valid UUID.");
    process.exit(1);
  }

  if (isSessionIdInUse(validatedId)) {
    console.error(`Error: Session ID ${validatedId} is already in use.`);
    process.exit(1);
  }
}

/**
 * 处理系统提示
 * @param {string} systemPrompt - 系统提示
 * @param {string} systemPromptFile - 系统提示文件路径
 * @returns {Promise<string>} 最终的系统提示
 * @original let c = Z.systemPrompt; if (Z.systemPromptFile) { ... }
 */
async function processSystemPrompt(systemPrompt, systemPromptFile) {
  if (systemPrompt && systemPromptFile) {
    console.error("Error: Cannot use both --system-prompt and --system-prompt-file. Please use only one.");
    process.exit(1);
  }

  if (systemPromptFile) {
    try {
      const resolvedPath = resolveFilePath(systemPromptFile);
      if (!fileExists(resolvedPath)) {
        console.error(`Error: System prompt file not found: ${resolvedPath}`);
        process.exit(1);
      }
      return readFileSync(resolvedPath, "utf8");
    } catch (error) {
      console.error(`Error reading system prompt file: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }

  return systemPrompt;
}

// 这些函数需要在其他模块中实现
function parsePermissionMode(options) {
  // @todo: 实现c6B()函数的逻辑
  return "ask";
}

async function processMcpConfig(mcpConfig) {
  // @todo: 实现MCP配置解析逻辑
  return {};
}

async function checkAuthenticationStatus() {
  // @todo: 实现sc()函数的逻辑
  return true;
}

function exitWithError() {
  // @todo: 实现$v1()函数的逻辑
  process.exit(1);
}

function parseToolPermissions(options) {
  // @todo: 实现l6B()函数的逻辑
  return { toolPermissionContext: {}, warnings: [] };
}

function getAllMcpServers() {
  // @todo: 实现Qz()函数的逻辑
  return {};
}

function validateInputFormat(inputFormat, outputFormat) {
  if (inputFormat && inputFormat !== "text" && inputFormat !== "stream-json") {
    console.error(`Error: Invalid input format "${inputFormat}".`);
    process.exit(1);
  }

  if (inputFormat === "stream-json" && outputFormat !== "stream-json") {
    console.error("Error: --input-format=stream-json requires output-format=stream-json.");
    process.exit(1);
  }
}

async function processInput(prompt, inputFormat) {
  // @todo: 实现zR8()函数的逻辑
  return prompt;
}

function getCurrentWorkingDirectory() {
  return process.cwd();
}

async function loadGlobalCommands() {
  // @todo: 实现th1()函数的逻辑
  return [];
}

async function initializeMcpResources(mcpServers) {
  // @todo: 实现Pq0()函数的逻辑
  return { clients: [], tools: [], commands: [] };
}

function logTelemetry(event, data) {
  // @todo: 实现C1()函数的逻辑
  console.log(`Telemetry: ${event}`, data);
}

function enableJsonOutput(enabled) {
  // @todo: 实现go0()函数的逻辑
  console.log(`JSON output enabled: ${enabled}`);
}

function applySettings(settingsPath) {
  // @todo: 实现Ay0()函数的逻辑
  console.log(`Applying settings from: ${settingsPath}`);
}

function refreshSettings() {
  // @todo: 实现l91()函数的逻辑
  console.log("Refreshing settings");
}
