/**
 * MCP命令处理模块
 * @description Claude Code的MCP相关CLI命令实现
 * @original 原始代码在57686-57878行
 */

import { validateScope, validateTransportType, parseHeaders } from '../../services/mcp-service.js';
import { validateAndAddMcpServer, getConfigPath, getAllMcpServers, findMcpServer } from '../../services/config-service.js';

/**
 * 创建MCP命令组
 * @param {Object} program - Commander.js程序实例
 * @returns {Object} MCP命令组
 * @original let Q = A.command("mcp").description("Configure and manage MCP servers").helpOption("-h, --help", "Display help for command");
 */
export function createMcpCommands(program) {
  const mcpCommand = program
    .command("mcp")
    .description("Configure and manage MCP servers")
    .helpOption("-h, --help", "Display help for command");

  // MCP服务器启动命令
  mcpCommand
    .command("serve")
    .description("Start the Claude Code MCP server")
    .helpOption("-h, --help", "Display help for command")
    .option("-d, --debug", "Enable debug mode", () => true)
    .option("--verbose", "Override verbose mode setting from config", () => true)
    .action(async ({ debug, verbose }) => {
      const workingDirectory = getCurrentWorkingDirectory();
      
      logTelemetry("tengu_mcp_start", {});
      
      if (!directoryExists(workingDirectory)) {
        console.error(`Error: Directory ${workingDirectory} does not exist`);
        process.exit(1);
      }
      
      try {
        await startMcpServer({ debug, verbose, workingDirectory });
      } catch (error) {
        console.error("Error: Failed to start MCP server:", error);
        process.exit(1);
      }
    });

  // 添加MCP服务器命令
  mcpCommand
    .command("add <name> <commandOrUrl> [args...]")
    .description("Add a server")
    .option("-s, --scope <scope>", "Configuration scope (local, user, or project)", "local")
    .option("-t, --transport <transport>", "Transport type (stdio, sse, http)", "stdio")
    .option("-e, --env <env...>", "Set environment variables (e.g. -e KEY=value)")
    .option("-H, --header <header...>", 'Set HTTP headers for SSE and HTTP transports (e.g. -H "X-Api-Key: abc123" -H "X-Custom: value")')
    .helpOption("-h, --help", "Display help for command")
    .action(async (serverName, commandOrUrl, args, options) => {
      if (!serverName) {
        console.error("Error: Server name is required.");
        console.error("Usage: claude mcp add <name> <command> [args...]");
        process.exit(1);
      }
      
      if (!commandOrUrl) {
        console.error("Error: Command is required when server name is provided.");
        console.error("Usage: claude mcp add <name> <command> [args...]");
        process.exit(1);
      }

      try {
        const scope = validateScope(options.scope);
        const transport = validateTransportType(options.transport);
        
        logTelemetry("tengu_mcp_add", {
          type: transport,
          scope: scope,
          source: "command",
          transport: transport
        });

        let serverConfig;
        
        if (transport === "sse") {
          if (!commandOrUrl) {
            console.error("Error: URL is required for SSE transport.");
            process.exit(1);
          }
          
          const headers = options.header ? parseHeaders(options.header) : undefined;
          serverConfig = {
            type: "sse",
            url: commandOrUrl,
            ...(headers && { headers })
          };
        } else if (transport === "http") {
          if (!commandOrUrl) {
            console.error("Error: URL is required for HTTP transport.");
            process.exit(1);
          }
          
          const headers = options.header ? parseHeaders(options.header) : undefined;
          serverConfig = {
            type: "http",
            url: commandOrUrl,
            ...(headers && { headers })
          };
        } else {
          // stdio transport
          const env = parseEnvironmentVariables(options.env || []);
          serverConfig = {
            command: commandOrUrl,
            args: args || [],
            env: env
          };
        }

        validateAndAddMcpServer(serverName, serverConfig, scope);
        
        if (transport === "stdio") {
          console.log(`Added stdio MCP server ${serverName} with command: ${commandOrUrl} ${(args || []).join(" ")} to ${scope} config`);
        } else {
          console.log(`Added ${transport} MCP server ${serverName} to ${scope} config`);
        }
        
        console.log(`File modified: ${getConfigPath(scope)}`);
        process.exit(0);
      } catch (error) {
        console.error(error.message);
        process.exit(1);
      }
    });

  // 移除MCP服务器命令
  mcpCommand
    .command("remove <name>")
    .description("Remove an MCP server")
    .option("-s, --scope <scope>", "Configuration scope (local, user, or project) - if not specified, removes from whichever scope it exists in")
    .helpOption("-h, --help", "Display help for command")
    .action(async (serverName, options) => {
      try {
        if (options.scope) {
          const scope = validateScope(options.scope);
          
          logTelemetry("tengu_mcp_delete", {
            name: serverName,
            scope: scope
          });
          
          removeMcpServer(serverName, scope);
          console.log(`Removed MCP server ${serverName} from ${scope} config`);
          console.log(`File modified: ${getConfigPath(scope)}`);
          process.exit(0);
        }

        // 查找服务器存在的作用域
        const scopes = findMcpServerScopes(serverName);
        
        if (scopes.length === 0) {
          console.error(`No MCP server found with name: "${serverName}"`);
          process.exit(1);
        } else if (scopes.length === 1) {
          const scope = scopes[0];
          
          logTelemetry("tengu_mcp_delete", {
            name: serverName,
            scope: scope
          });
          
          removeMcpServer(serverName, scope);
          console.log(`Removed MCP server "${serverName}" from ${scope} config`);
          console.log(`File modified: ${getConfigPath(scope)}`);
          process.exit(0);
        } else {
          console.error(`MCP server "${serverName}" exists in multiple scopes:`);
          scopes.forEach(scope => {
            console.error(`  - ${getScopeDisplayName(scope)} (${getConfigPath(scope)})`);
          });
          console.error(`\nTo remove from a specific scope, use:`);
          scopes.forEach(scope => {
            console.error(`  claude mcp remove "${serverName}" -s ${scope}`);
          });
          process.exit(1);
        }
      } catch (error) {
        console.error(error.message);
        process.exit(1);
      }
    });

  // 列出MCP服务器命令
  mcpCommand
    .command("list")
    .description("List configured MCP servers")
    .helpOption("-h, --help", "Display help for command")
    .action(async () => {
      logTelemetry("tengu_mcp_list", {});
      
      const allServers = getAllMcpServers();
      
      if (Object.keys(allServers).length === 0) {
        console.log("No MCP servers configured. Use `claude mcp add` to add a server.");
      } else {
        console.log(`Checking MCP server health...\n`);
        
        for (const [serverName, serverConfig] of Object.entries(allServers)) {
          const status = await checkMcpServerStatus(serverName, serverConfig);
          
          if (serverConfig.type === "sse") {
            console.log(`${serverName}: ${serverConfig.url} (SSE) - ${status}`);
          } else if (serverConfig.type === "http") {
            console.log(`${serverName}: ${serverConfig.url} (HTTP) - ${status}`);
          } else if (!serverConfig.type || serverConfig.type === "stdio") {
            const args = Array.isArray(serverConfig.args) ? serverConfig.args : [];
            console.log(`${serverName}: ${serverConfig.command} ${args.join(" ")} - ${status}`);
          }
        }
      }
      
      process.exit(0);
    });

  // 获取MCP服务器详情命令
  mcpCommand
    .command("get <name>")
    .description("Get details about an MCP server")
    .helpOption("-h, --help", "Display help for command")
    .action(async (serverName) => {
      logTelemetry("tengu_mcp_get", {
        name: serverName
      });

      const serverConfig = findMcpServer(serverName);
      
      if (!serverConfig) {
        console.error(`No MCP server found with name: ${serverName}`);
        process.exit(1);
      }

      console.log(`${serverName}:`);
      console.log(`  Scope: ${getScopeDisplayName(serverConfig.scope)}`);
      
      const status = await checkMcpServerStatus(serverName, serverConfig);
      console.log(`  Status: ${status}`);

      if (serverConfig.type === "sse") {
        console.log("  Type: sse");
        console.log(`  URL: ${serverConfig.url}`);
        
        if (serverConfig.headers) {
          console.log("  Headers:");
          for (const [name, value] of Object.entries(serverConfig.headers)) {
            console.log(`    ${name}: ${value}`);
          }
        }
      } else if (serverConfig.type === "http") {
        console.log("  Type: http");
        console.log(`  URL: ${serverConfig.url}`);
        
        if (serverConfig.headers) {
          console.log("  Headers:");
          for (const [name, value] of Object.entries(serverConfig.headers)) {
            console.log(`    ${name}: ${value}`);
          }
        }
      } else {
        console.log("  Type: stdio");
        console.log(`  Command: ${serverConfig.command}`);
        
        if (serverConfig.args && serverConfig.args.length > 0) {
          console.log(`  Args: ${serverConfig.args.join(" ")}`);
        }
        
        if (serverConfig.env && Object.keys(serverConfig.env).length > 0) {
          console.log("  Environment:");
          for (const [name, value] of Object.entries(serverConfig.env)) {
            console.log(`    ${name}=${value}`);
          }
        }
      }

      console.log(`\nTo remove this server, run: claude mcp remove "${serverName}" -s ${serverConfig.scope}`);
      process.exit(0);
    });

  return mcpCommand;
}

// 辅助函数 - 这些需要在其他模块中实现
function getCurrentWorkingDirectory() {
  return process.cwd();
}

function directoryExists(path) {
  const fs = require('fs');
  return fs.existsSync(path);
}

function logTelemetry(event, data) {
  // @todo: 实现C1()函数的逻辑
  console.log(`Telemetry: ${event}`, data);
}

function parseEnvironmentVariables(envArray) {
  const env = {};
  for (const envVar of envArray) {
    const [key, value] = envVar.split('=');
    if (key && value) {
      env[key] = value;
    }
  }
  return env;
}

async function startMcpServer(options) {
  // @todo: 实现MCP服务器启动逻辑
  throw new Error("startMcpServer not implemented");
}

function removeMcpServer(serverName, scope) {
  // @todo: 实现br1()函数的逻辑
  throw new Error("removeMcpServer not implemented");
}

function findMcpServerScopes(serverName) {
  // @todo: 实现查找服务器存在的作用域逻辑
  return [];
}

async function checkMcpServerStatus(serverName, serverConfig) {
  // @todo: 实现ScB()函数的逻辑
  return "✓ Connected";
}

function getScopeDisplayName(scope) {
  // @todo: 实现Yh()函数的逻辑
  return scope;
}
