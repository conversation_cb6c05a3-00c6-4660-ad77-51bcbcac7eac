/**
 * Claude Code CLI主入口
 * @description Claude Code命令行工具的主要入口点
 * @original 原始代码在57284-57930行
 */

import { Command, Option } from 'commander';
import { createMcpCommands } from './commands/mcp.js';
import { createConfigCommands } from './commands/config.js';

/**
 * 创建主CLI程序
 * @returns {Command} Commander.js程序实例
 * @original let A = new gdB(); A.name("claude").description("<PERSON> Code - starts an interactive session by default, use -p/--print for non-interactive output")...
 */
export function createCliProgram() {
  const program = new Command();

  // 主程序配置
  program
    .name("claude")
    .description("<PERSON> Code - starts an interactive session by default, use -p/--print for non-interactive output")
    .argument("[prompt]", "Your prompt", String)
    .helpOption("-h, --help", "Display help for command")
    .version("1.0.72", "-v, --version", "Output the version number");

  // 调试选项
  program
    .option("-d, --debug", "Enable debug mode", () => true)
    .addOption(new Option("-d2e, --debug-to-stderr", "Enable debug mode (to stderr)")
      .argParser(Boolean)
      .hideHelp())
    .option("--verbose", "Override verbose mode setting from config", () => true)
    .option("--mcp-debug", "[DEPRECATED. Use --debug instead] Enable MCP debug mode (shows MCP server errors)", () => true);

  // 输出选项
  program
    .option("-p, --print", "Print response and exit (useful for pipes)", () => true)
    .addOption(new Option("--output-format <format>", 'Output format (only works with --print): "text" (default), "json" (single result), or "stream-json" (realtime streaming)')
      .choices(["text", "json", "stream-json"]))
    .addOption(new Option("--input-format <format>", 'Input format (only works with --print): "text" (default), or "stream-json" (realtime streaming input)')
      .choices(["text", "stream-json"]));

  // 会话选项
  program
    .option("-c, --continue", "Continue the most recent conversation", () => true)
    .option("-r, --resume [sessionId]", "Resume a conversation - provide a session ID or interactively select a conversation to resume", value => value || true)
    .option("--session-id <uuid>", "Use a specific session ID for the conversation (must be a valid UUID)");

  // 模型选项
  program
    .option("--model <model>", "Model for the current session. Provide an alias for the latest model (e.g. 'sonnet' or 'opus') or a model's full name (e.g. 'claude-sonnet-4-20250514').")
    .option("--fallback-model <model>", "Enable automatic fallback to specified model when default model is overloaded (only works with --print)");

  // 系统提示选项
  program
    .addOption(new Option("--system-prompt <prompt>", "System prompt to use for the session  (only works with --print)")
      .argParser(String)
      .hideHelp())
    .addOption(new Option("--system-prompt-file <file>", "Read system prompt from a file (only works with --print)")
      .argParser(String)
      .hideHelp())
    .addOption(new Option("--append-system-prompt <prompt>", "Append a system prompt to the default system prompt")
      .argParser(String));

  // 权限和安全选项
  program
    .option("--dangerously-skip-permissions", "Bypass all permission checks. Recommended only for sandboxes with no internet access.", () => true)
    .addOption(new Option("--permission-mode <mode>", "Permission mode to use for the session")
      .argParser(String)
      .choices(getValidPermissionModes()))
    .addOption(new Option("--permission-prompt-tool <tool>", "MCP tool to use for permission prompts (only works with --print)")
      .argParser(String)
      .hideHelp());

  // 工具选项
  program
    .option("--allowedTools <tools...>", 'Comma or space-separated list of tool names to allow (e.g. "Bash(git:*) Edit")')
    .option("--disallowedTools <tools...>", 'Comma or space-separated list of tool names to deny (e.g. "Bash(git:*) Edit")');

  // MCP配置选项
  program
    .option("--mcp-config <file or string>", "Load MCP servers from a JSON file or string")
    .option("--strict-mcp-config", "Only use MCP servers from --mcp-config, ignoring all other MCP configurations", () => true);

  // 其他选项
  program
    .option("--settings <file-or-json>", "Path to a settings JSON file or a JSON string to load additional settings from")
    .option("--add-dir <directories...>", "Additional directories to allow tool access to")
    .option("--ide", "Automatically connect to IDE on startup if exactly one valid IDE is available", () => true)
    .addOption(new Option("--max-turns <turns>", "Maximum number of agentic turns in non-interactive mode. This will early exit the conversation after the specified number of turns. (only works with --print)")
      .argParser(Number)
      .hideHelp());

  // 隐藏选项
  program
    .addOption(new Option("--teleport [session]", "Resume a teleport session, optionally specify session ID")
      .hideHelp())
    .addOption(new Option("--remote <description>", "Create a remote session with the given description")
      .hideHelp());

  // 主命令处理
  program.action(async (prompt, options) => {
    const {
      debug = false,
      debugToStderr = false,
      verbose = false,
      print: printMode,
      outputFormat,
      inputFormat,
      continue: continueSession,
      resume,
      model,
      fallbackModel,
      systemPrompt,
      systemPromptFile,
      appendSystemPrompt,
      dangerouslySkipPermissions,
      permissionMode,
      allowedTools,
      disallowedTools,
      mcpConfig,
      strictMcpConfig,
      settings,
      addDir,
      ide,
      sessionId,
      maxTurns,
      permissionPromptTool,
      teleport,
      remote
    } = options;

    try {
      // 验证会话ID格式
      if (sessionId && !isValidUuid(sessionId)) {
        console.error("Error: Invalid session ID. Must be a valid UUID.");
        process.exit(1);
      }

      // 检查会话ID冲突
      if (sessionId && isSessionIdInUse(sessionId)) {
        console.error(`Error: Session ID ${sessionId} is already in use.`);
        process.exit(1);
      }

      // 验证模型配置
      if (fallbackModel && model && fallbackModel === model) {
        console.error("Error: Fallback model cannot be the same as the main model. Please specify a different model for --fallback-model.");
        process.exit(1);
      }

      // 验证系统提示配置
      if (systemPrompt && systemPromptFile) {
        console.error("Error: Cannot use both --system-prompt and --system-prompt-file. Please use only one.");
        process.exit(1);
      }

      // 处理系统提示文件
      let finalSystemPrompt = systemPrompt;
      if (systemPromptFile) {
        const resolvedPath = resolvePath(systemPromptFile);
        if (!fileExists(resolvedPath)) {
          console.error(`Error: System prompt file not found: ${resolvedPath}`);
          process.exit(1);
        }
        finalSystemPrompt = readFileSync(resolvedPath, 'utf8');
      }

      // 启动主应用程序
      await startClaudeCodeSession({
        prompt,
        debug,
        debugToStderr,
        verbose,
        printMode,
        outputFormat,
        inputFormat,
        continueSession,
        resume,
        model,
        fallbackModel,
        systemPrompt: finalSystemPrompt,
        appendSystemPrompt,
        dangerouslySkipPermissions,
        permissionMode,
        allowedTools,
        disallowedTools,
        mcpConfig,
        strictMcpConfig,
        settings,
        addDir,
        ide,
        sessionId,
        maxTurns,
        permissionPromptTool,
        teleport,
        remote
      });
    } catch (error) {
      console.error("Error:", error.message);
      process.exit(1);
    }
  });

  // 添加子命令
  createConfigCommands(program);
  createMcpCommands(program);
  
  // 系统管理命令
  program
    .command("migrate-installer")
    .description("Migrate from global npm installation to local installation")
    .helpOption("-h, --help", "Display help for command")
    .action(async () => {
      if (isRunningFromLocalInstallation()) {
        console.log("Already running from local installation. No migration needed.");
        process.exit(0);
      }
      
      logTelemetry("tengu_migrate_installer_command", {});
      await runMigrationProcess();
      process.exit(0);
    });

  program
    .command("setup-token")
    .description("Set up a long-lived authentication token (requires Claude subscription)")
    .helpOption("-h, --help", "Display help for command")
    .action(async () => {
      logTelemetry("tengu_setup_token_command", {});
      
      await initializeAuth();
      
      if (!shouldSetupOAuthToken()) {
        console.warn("Warning: You already have authentication configured via environment variable or API key helper.");
        console.warn("The setup-token command will create a new OAuth token which you can use instead.");
      }
      
      await runTokenSetupProcess();
      process.exit(0);
    });

  program
    .command("doctor")
    .description("Check the health of your Claude Code auto-updater")
    .helpOption("-h, --help", "Display help for command")
    .action(async () => {
      logTelemetry("tengu_doctor_command", {});
      await runHealthCheck();
      process.exit(0);
    });

  program
    .command("update")
    .description("Check for updates and install if available")
    .helpOption("-h, --help", "Display help for command")
    .action(checkForUpdates);

  program
    .command("install [target]")
    .description("Install Claude Code native build. Use [target] to specify version (stable, latest, or specific version)")
    .option("--force", "Force installation even if already installed")
    .helpOption("-h, --help", "Display help for command")
    .action(async (target, options) => {
      await initializeWorkingDirectory();
      await runInstallProcess(target, options);
    });

  return program;
}

// 辅助函数 - 这些需要在其他模块中实现
function getValidPermissionModes() {
  // @todo: 实现_K1的逻辑
  return ["ask", "allow", "deny"];
}

function isValidUuid(uuid) {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

function isSessionIdInUse(sessionId) {
  // @todo: 实现G$2()函数的逻辑
  return false;
}

function resolvePath(path) {
  // @todo: 实现PcB()函数的逻辑
  return path;
}

function fileExists(path) {
  const fs = require('fs');
  return fs.existsSync(path);
}

function readFileSync(path, encoding) {
  const fs = require('fs');
  return fs.readFileSync(path, encoding);
}

async function startClaudeCodeSession(options) {
  // @todo: 实现主会话启动逻辑
  console.log("Starting Claude Code session with options:", options);
}

function logTelemetry(event, data) {
  // @todo: 实现C1()函数的逻辑
  console.log(`Telemetry: ${event}`, data);
}

function isRunningFromLocalInstallation() {
  // @todo: 实现Gv()函数的逻辑
  return false;
}

async function runMigrationProcess() {
  // @todo: 实现迁移流程
  console.log("Running migration process...");
}

async function initializeAuth() {
  // @todo: 实现J7()函数的逻辑
  console.log("Initializing authentication...");
}

function shouldSetupOAuthToken() {
  // @todo: 实现xz()函数的逻辑
  return true;
}

async function runTokenSetupProcess() {
  // @todo: 实现令牌设置流程
  console.log("Running token setup process...");
}

async function runHealthCheck() {
  // @todo: 实现健康检查流程
  console.log("Running health check...");
}

async function checkForUpdates() {
  // @todo: 实现LcB()函数的逻辑
  console.log("Checking for updates...");
}

async function initializeWorkingDirectory() {
  // @todo: 实现Ib()函数的逻辑
  console.log("Initializing working directory...");
}

async function runInstallProcess(target, options) {
  // @todo: 实现安装流程
  console.log("Running install process...", { target, options });
}
