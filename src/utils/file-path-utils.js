/**
 * 文件路径处理工具
 * @description 重构自原始文件中的文件路径处理代码，对应第2697-2700行
 * @original 原始代码行 2697-2700
 */

import { fileURLToPath } from "node:url";
import * as path from "node:path";
import { execFile } from "child_process";
import { promisify } from "util";

/**
 * 异步execFile函数
 */
const execFileAsync = promisify(execFile);

/**
 * 文件路径工具类
 * @description 提供各种文件路径操作的便捷方法
 */
export class FilePathUtils {
  /**
   * 将文件URL转换为路径
   * @param {string|URL} url - 文件URL
   * @returns {string} 文件路径
   */
  static urlToPath(url) {
    return fileURLToPath(url);
  }

  /**
   * 将路径转换为文件URL
   * @param {string} filePath - 文件路径
   * @returns {string} 文件URL
   */
  static pathToUrl(filePath) {
    return new URL(`file://${filePath}`).href;
  }

  /**
   * 获取当前模块的目录路径
   * @param {string} importMetaUrl - import.meta.url
   * @returns {string} 目录路径
   */
  static getCurrentDir(importMetaUrl) {
    return path.dirname(fileURLToPath(importMetaUrl));
  }

  /**
   * 获取当前模块的文件路径
   * @param {string} importMetaUrl - import.meta.url
   * @returns {string} 文件路径
   */
  static getCurrentFile(importMetaUrl) {
    return fileURLToPath(importMetaUrl);
  }

  /**
   * 规范化路径
   * @param {string} filePath - 文件路径
   * @returns {string} 规范化后的路径
   */
  static normalize(filePath) {
    return path.normalize(filePath);
  }

  /**
   * 解析路径
   * @param {...string} paths - 路径片段
   * @returns {string} 解析后的路径
   */
  static resolve(...paths) {
    return path.resolve(...paths);
  }

  /**
   * 连接路径
   * @param {...string} paths - 路径片段
   * @returns {string} 连接后的路径
   */
  static join(...paths) {
    return path.join(...paths);
  }

  /**
   * 获取相对路径
   * @param {string} from - 起始路径
   * @param {string} to - 目标路径
   * @returns {string} 相对路径
   */
  static relative(from, to) {
    return path.relative(from, to);
  }

  /**
   * 获取路径的目录名
   * @param {string} filePath - 文件路径
   * @returns {string} 目录名
   */
  static dirname(filePath) {
    return path.dirname(filePath);
  }

  /**
   * 获取路径的基础名（文件名）
   * @param {string} filePath - 文件路径
   * @param {string} ext - 要移除的扩展名（可选）
   * @returns {string} 基础名
   */
  static basename(filePath, ext) {
    return path.basename(filePath, ext);
  }

  /**
   * 获取路径的扩展名
   * @param {string} filePath - 文件路径
   * @returns {string} 扩展名
   */
  static extname(filePath) {
    return path.extname(filePath);
  }

  /**
   * 解析路径为对象
   * @param {string} filePath - 文件路径
   * @returns {Object} 路径对象 {root, dir, base, ext, name}
   */
  static parse(filePath) {
    return path.parse(filePath);
  }

  /**
   * 从路径对象格式化路径
   * @param {Object} pathObject - 路径对象
   * @returns {string} 格式化后的路径
   */
  static format(pathObject) {
    return path.format(pathObject);
  }

  /**
   * 检查路径是否为绝对路径
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为绝对路径
   */
  static isAbsolute(filePath) {
    return path.isAbsolute(filePath);
  }

  /**
   * 获取路径分隔符
   * @returns {string} 路径分隔符
   */
  static get separator() {
    return path.sep;
  }

  /**
   * 获取路径定界符
   * @returns {string} 路径定界符
   */
  static get delimiter() {
    return path.delimiter;
  }

  /**
   * 获取POSIX路径操作
   * @returns {Object} POSIX路径操作对象
   */
  static get posix() {
    return path.posix;
  }

  /**
   * 获取Windows路径操作
   * @returns {Object} Windows路径操作对象
   */
  static get win32() {
    return path.win32;
  }

  /**
   * 转换为POSIX路径
   * @param {string} filePath - 文件路径
   * @returns {string} POSIX路径
   */
  static toPosix(filePath) {
    return filePath.split(path.sep).join(path.posix.sep);
  }

  /**
   * 转换为Windows路径
   * @param {string} filePath - 文件路径
   * @returns {string} Windows路径
   */
  static toWin32(filePath) {
    return filePath.split(path.posix.sep).join(path.win32.sep);
  }

  /**
   * 确保路径以指定字符结尾
   * @param {string} filePath - 文件路径
   * @param {string} suffix - 后缀字符，默认为路径分隔符
   * @returns {string} 处理后的路径
   */
  static ensureTrailing(filePath, suffix = path.sep) {
    return filePath.endsWith(suffix) ? filePath : filePath + suffix;
  }

  /**
   * 确保路径不以指定字符结尾
   * @param {string} filePath - 文件路径
   * @param {string} suffix - 后缀字符，默认为路径分隔符
   * @returns {string} 处理后的路径
   */
  static removeTrailing(filePath, suffix = path.sep) {
    return filePath.endsWith(suffix) ? filePath.slice(0, -suffix.length) : filePath;
  }

  /**
   * 获取路径的深度
   * @param {string} filePath - 文件路径
   * @returns {number} 路径深度
   */
  static getDepth(filePath) {
    const normalized = this.normalize(filePath);
    const parts = normalized.split(path.sep).filter(part => part !== '');
    return parts.length;
  }

  /**
   * 获取两个路径的公共祖先路径
   * @param {string} path1 - 第一个路径
   * @param {string} path2 - 第二个路径
   * @returns {string} 公共祖先路径
   */
  static getCommonAncestor(path1, path2) {
    const parts1 = this.resolve(path1).split(path.sep);
    const parts2 = this.resolve(path2).split(path.sep);
    
    const commonParts = [];
    const minLength = Math.min(parts1.length, parts2.length);
    
    for (let i = 0; i < minLength; i++) {
      if (parts1[i] === parts2[i]) {
        commonParts.push(parts1[i]);
      } else {
        break;
      }
    }
    
    return commonParts.join(path.sep) || path.sep;
  }

  /**
   * 检查路径是否在指定目录内
   * @param {string} filePath - 文件路径
   * @param {string} directory - 目录路径
   * @returns {boolean} 是否在目录内
   */
  static isWithin(filePath, directory) {
    const resolvedPath = this.resolve(filePath);
    const resolvedDir = this.resolve(directory);
    
    return resolvedPath.startsWith(resolvedDir + path.sep) || resolvedPath === resolvedDir;
  }

  /**
   * 获取路径中的所有父目录
   * @param {string} filePath - 文件路径
   * @returns {string[]} 父目录数组
   */
  static getParents(filePath) {
    const resolved = this.resolve(filePath);
    const parents = [];
    let current = this.dirname(resolved);
    
    while (current !== this.dirname(current)) {
      parents.push(current);
      current = this.dirname(current);
    }
    
    parents.push(current); // 添加根目录
    return parents;
  }

  /**
   * 创建安全的文件名
   * @param {string} filename - 原始文件名
   * @param {string} replacement - 替换字符，默认为下划线
   * @returns {string} 安全的文件名
   */
  static sanitizeFilename(filename, replacement = '_') {
    // 移除或替换不安全的字符
    return filename.replace(/[<>:"/\\|?*\x00-\x1f]/g, replacement);
  }

  /**
   * 生成唯一的文件路径
   * @param {string} basePath - 基础路径
   * @param {string} extension - 文件扩展名（可选）
   * @returns {string} 唯一的文件路径
   */
  static generateUniquePath(basePath, extension = '') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const uniqueSuffix = `${timestamp}_${random}`;
    
    if (extension && !extension.startsWith('.')) {
      extension = '.' + extension;
    }
    
    return this.join(basePath, `file_${uniqueSuffix}${extension}`);
  }
}

/**
 * 执行文件工具类
 * @description 提供执行外部程序的便捷方法
 */
export class ExecFileUtils {
  /**
   * 异步执行文件
   * @param {string} file - 要执行的文件
   * @param {string[]} args - 参数数组
   * @param {Object} options - 执行选项
   * @returns {Promise<{stdout: string, stderr: string}>} 执行结果
   */
  static async exec(file, args = [], options = {}) {
    try {
      const result = await execFileAsync(file, args, options);
      return {
        stdout: result.stdout,
        stderr: result.stderr,
        success: true
      };
    } catch (error) {
      return {
        stdout: error.stdout || '',
        stderr: error.stderr || '',
        error: error.message,
        code: error.code,
        success: false
      };
    }
  }

  /**
   * 同步执行文件
   * @param {string} file - 要执行的文件
   * @param {string[]} args - 参数数组
   * @param {Object} options - 执行选项
   * @returns {Object} 执行结果
   */
  static execSync(file, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      execFile(file, args, options, (error, stdout, stderr) => {
        if (error) {
          resolve({
            stdout: stdout || '',
            stderr: stderr || '',
            error: error.message,
            code: error.code,
            success: false
          });
        } else {
          resolve({
            stdout,
            stderr,
            success: true
          });
        }
      });
    });
  }

  /**
   * 检查命令是否存在
   * @param {string} command - 命令名称
   * @returns {Promise<boolean>} 命令是否存在
   */
  static async commandExists(command) {
    const isWindows = process.platform === 'win32';
    const checkCommand = isWindows ? 'where' : 'which';
    
    try {
      const result = await this.exec(checkCommand, [command]);
      return result.success && result.stdout.trim().length > 0;
    } catch {
      return false;
    }
  }

  /**
   * 获取命令的完整路径
   * @param {string} command - 命令名称
   * @returns {Promise<string|null>} 命令的完整路径或null
   */
  static async getCommandPath(command) {
    const isWindows = process.platform === 'win32';
    const checkCommand = isWindows ? 'where' : 'which';
    
    try {
      const result = await this.exec(checkCommand, [command]);
      if (result.success) {
        return result.stdout.trim().split('\n')[0];
      }
    } catch {
      // 忽略错误
    }
    
    return null;
  }
}

// 导出便捷函数
export const {
  urlToPath,
  pathToUrl,
  getCurrentDir,
  getCurrentFile,
  normalize,
  resolve,
  join,
  relative,
  dirname,
  basename,
  extname,
  parse,
  format,
  isAbsolute,
  toPosix,
  toWin32,
  sanitizeFilename,
  generateUniquePath
} = FilePathUtils;

// 导出路径常量
export const { separator, delimiter, posix, win32 } = FilePathUtils;
