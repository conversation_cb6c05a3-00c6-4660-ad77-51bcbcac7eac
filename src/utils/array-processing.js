/**
 * 数组处理工具函数
 * @description 重构自原始文件中的数组处理函数，对应第1363-1389行
 * @original 原始代码行 1363-1389
 */

/**
 * 在数组中查找满足条件的元素索引
 * @param {Array} array - 要搜索的数组
 * @param {Function} predicate - 判断函数
 * @param {number} fromIndex - 开始搜索的索引
 * @param {boolean} fromRight - 是否从右向左搜索
 * @returns {number} 找到的元素索引，未找到返回-1
 * @original function alB(A, B, Q, D) { var Z = A.length, G = Q + (D ? 1 : -1); while (D ? G-- : ++G < Z) if (B(A[G], G, A)) return G; return -1; }
 */
export function baseFindIndex(array, predicate, fromIndex, fromRight) {
  const length = array.length;
  let index = fromIndex + (fromRight ? 1 : -1);
  
  while (fromRight ? index-- : ++index < length) {
    if (predicate(array[index], index, array)) {
      return index;
    }
  }
  
  return -1;
}

/**
 * 检查值是否为NaN
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为NaN
 * @original function slB(A) { return A !== A; }
 */
export function isNaN(value) {
  return value !== value;
}

/**
 * 在数组中查找指定值的索引（严格相等）
 * @param {Array} array - 要搜索的数组
 * @param {*} value - 要查找的值
 * @param {number} fromIndex - 开始搜索的索引
 * @returns {number} 找到的元素索引，未找到返回-1
 * @original function rlB(A, B, Q) { var D = Q - 1, Z = A.length; while (++D < Z) if (A[D] === B) return D; return -1; }
 */
function strictIndexOf(array, value, fromIndex) {
  let index = fromIndex - 1;
  const length = array.length;
  
  while (++index < length) {
    if (array[index] === value) {
      return index;
    }
  }
  
  return -1;
}

/**
 * 在数组中查找指定值的索引（支持NaN）
 * @param {Array} array - 要搜索的数组
 * @param {*} value - 要查找的值
 * @param {number} fromIndex - 开始搜索的索引
 * @returns {number} 找到的元素索引，未找到返回-1
 * @original function olB(A, B, Q) { return B === B ? iT0(A, B, Q) : lT0(A, pT0, Q); }
 */
export function baseIndexOf(array, value, fromIndex) {
  // 如果值不是NaN，使用严格相等比较
  if (value === value) {
    return strictIndexOf(array, value, fromIndex);
  }
  
  // 如果值是NaN，使用NaN检查函数
  return baseFindIndex(array, isNaN, fromIndex);
}

/**
 * 检查数组是否包含指定值
 * @param {Array} array - 要检查的数组
 * @param {*} value - 要查找的值
 * @returns {boolean} 是否包含指定值
 * @original function tlB(A, B) { var Q = A == null ? 0 : A.length; return !!Q && nT0(A, B, 0) > -1; }
 */
export function arrayIncludes(array, value) {
  const length = array == null ? 0 : array.length;
  return !!length && baseIndexOf(array, value, 0) > -1;
}

/**
 * 查找数组中第一个满足条件的元素
 * @param {Array} array - 要搜索的数组
 * @param {Function} predicate - 判断函数
 * @param {number} fromIndex - 开始搜索的索引
 * @returns {*} 找到的元素，未找到返回undefined
 */
export function arrayFind(array, predicate, fromIndex = 0) {
  const index = baseFindIndex(array, predicate, fromIndex, false);
  return index > -1 ? array[index] : undefined;
}

/**
 * 查找数组中最后一个满足条件的元素
 * @param {Array} array - 要搜索的数组
 * @param {Function} predicate - 判断函数
 * @param {number} fromIndex - 开始搜索的索引
 * @returns {*} 找到的元素，未找到返回undefined
 */
export function arrayFindLast(array, predicate, fromIndex) {
  const length = array ? array.length : 0;
  const startIndex = fromIndex == null ? length - 1 : fromIndex;
  const index = baseFindIndex(array, predicate, startIndex, true);
  return index > -1 ? array[index] : undefined;
}

/**
 * 过滤数组中满足条件的元素
 * @param {Array} array - 要过滤的数组
 * @param {Function} predicate - 判断函数
 * @returns {Array} 过滤后的新数组
 */
export function arrayFilter(array, predicate) {
  let index = -1;
  let resIndex = 0;
  const length = array == null ? 0 : array.length;
  const result = [];
  
  while (++index < length) {
    const value = array[index];
    if (predicate(value, index, array)) {
      result[resIndex++] = value;
    }
  }
  
  return result;
}

/**
 * 对数组中的每个元素执行映射函数
 * @param {Array} array - 要映射的数组
 * @param {Function} iteratee - 映射函数
 * @returns {Array} 映射后的新数组
 */
export function arrayMap(array, iteratee) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  const result = Array(length);
  
  while (++index < length) {
    result[index] = iteratee(array[index], index, array);
  }
  
  return result;
}

/**
 * 对数组中的每个元素执行函数
 * @param {Array} array - 要遍历的数组
 * @param {Function} iteratee - 遍历函数
 * @returns {Array} 原数组
 */
export function arrayEach(array, iteratee) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  
  while (++index < length) {
    if (iteratee(array[index], index, array) === false) {
      break;
    }
  }
  
  return array;
}

/**
 * 从右向左对数组中的每个元素执行函数
 * @param {Array} array - 要遍历的数组
 * @param {Function} iteratee - 遍历函数
 * @returns {Array} 原数组
 */
export function arrayEachRight(array, iteratee) {
  const length = array == null ? 0 : array.length;
  let index = length;
  
  while (index--) {
    if (iteratee(array[index], index, array) === false) {
      break;
    }
  }
  
  return array;
}

/**
 * 使用累加器函数对数组进行归约
 * @param {Array} array - 要归约的数组
 * @param {Function} iteratee - 累加器函数
 * @param {*} accumulator - 初始值
 * @param {boolean} initAccum - 是否使用数组第一个元素作为初始值
 * @returns {*} 归约结果
 */
export function arrayReduce(array, iteratee, accumulator, initAccum) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  
  if (initAccum && length) {
    accumulator = array[++index];
  }
  
  while (++index < length) {
    accumulator = iteratee(accumulator, array[index], index, array);
  }
  
  return accumulator;
}

/**
 * 从右向左使用累加器函数对数组进行归约
 * @param {Array} array - 要归约的数组
 * @param {Function} iteratee - 累加器函数
 * @param {*} accumulator - 初始值
 * @param {boolean} initAccum - 是否使用数组最后一个元素作为初始值
 * @returns {*} 归约结果
 */
export function arrayReduceRight(array, iteratee, accumulator, initAccum) {
  const length = array == null ? 0 : array.length;
  let index = length;
  
  if (initAccum && length) {
    accumulator = array[--index];
  }
  
  while (index--) {
    accumulator = iteratee(accumulator, array[index], index, array);
  }
  
  return accumulator;
}

/**
 * 检查数组中是否有元素满足条件
 * @param {Array} array - 要检查的数组
 * @param {Function} predicate - 判断函数
 * @returns {boolean} 是否有元素满足条件
 */
export function arraySome(array, predicate) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  
  while (++index < length) {
    if (predicate(array[index], index, array)) {
      return true;
    }
  }
  
  return false;
}

/**
 * 检查数组中是否所有元素都满足条件
 * @param {Array} array - 要检查的数组
 * @param {Function} predicate - 判断函数
 * @returns {boolean} 是否所有元素都满足条件
 */
export function arrayEvery(array, predicate) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  
  while (++index < length) {
    if (!predicate(array[index], index, array)) {
      return false;
    }
  }
  
  return true;
}
