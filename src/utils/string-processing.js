/**
 * 字符串处理工具函数
 * @description 重构自原始文件中的字符串处理函数，对应第1314-1325行
 * @original 原始代码行 1314-1325
 */

/**
 * 空白字符正则表达式
 * @original var AlB = /\s/;
 */
const WHITESPACE_REGEX = /\s/;

/**
 * 行首空白字符正则表达式  
 * @original var QlB = /^\s+/;
 */
const LEADING_WHITESPACE_REGEX = /^\s+/;

/**
 * 查找字符串末尾非空白字符的索引
 * @param {string} str - 要处理的字符串
 * @returns {number} 最后一个非空白字符的索引
 * @original function BlB(A) { var B = A.length; while (B-- && AlB.test(A.charAt(B))); return B; }
 */
function findLastNonWhitespaceIndex(str) {
  let index = str.length;
  while (index-- && WHITESPACE_REGEX.test(str.charAt(index)));
  return index;
}

/**
 * 去除字符串首尾空白字符
 * @param {string} str - 要处理的字符串
 * @returns {string} 去除空白后的字符串
 * @original function DlB(A) { return A ? A.slice(0, RT0(A) + 1).replace(QlB, "") : A; }
 */
export function trimString(str) {
  return str ? str.slice(0, findLastNonWhitespaceIndex(str) + 1).replace(LEADING_WHITESPACE_REGEX, "") : str;
}

// 导出内部函数供测试使用
export { findLastNonWhitespaceIndex };
