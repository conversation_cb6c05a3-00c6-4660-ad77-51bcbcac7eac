/**
 * 字符串处理工具函数
 * @description 重构自原始文件中的Lodash字符串处理函数
 */

// 正则表达式常量
const WHITESPACE_REGEX = /\s/;
const LEADING_WHITESPACE_REGEX = /^\s+/;

/**
 * 去除字符串末尾的空白字符
 * @param {string} str - 要处理的字符串
 * @returns {number} 去除空白后的最后一个字符索引
 * @original function BlB(A) { var B = A.length; while (B-- && AlB.test(A.charAt(B))); return B; }
 */
function trimEndIndex(str) {
  let index = str.length;
  while (index-- && WHITESPACE_REGEX.test(str.charAt(index)));
  return index;
}

/**
 * 去除字符串首尾空白字符
 * @param {string} str - 要处理的字符串
 * @returns {string} 去除空白后的字符串
 * @original function DlB(A) { return A ? A.slice(0, RT0(A) + 1).replace(QlB, "") : A; }
 */
export function trim(str) {
  return str ? str.slice(0, trimEndIndex(str) + 1).replace(LEADING_WHITESPACE_REGEX, "") : str;
}

/**
 * 去除字符串末尾空白字符
 * @param {string} str - 要处理的字符串
 * @returns {string} 去除末尾空白后的字符串
 */
export function trimEnd(str) {
  if (!str) return str;
  return str.slice(0, trimEndIndex(str) + 1);
}

/**
 * 去除字符串开头空白字符
 * @param {string} str - 要处理的字符串
 * @returns {string} 去除开头空白后的字符串
 */
export function trimStart(str) {
  return str ? str.replace(LEADING_WHITESPACE_REGEX, "") : str;
}

/**
 * 检查字符串是否包含复杂Unicode字符
 * @param {string} str - 要检查的字符串
 * @returns {boolean} 是否包含复杂Unicode字符
 * @original function OnB(A) { return RnB.test(A); }
 */
const COMPLEX_UNICODE_REGEX = /[\ud800-\udfff\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff\ufe0e\ufe0f\u200d]/;
export function hasComplexUnicode(str) {
  return COMPLEX_UNICODE_REGEX.test(str);
}

/**
 * 将字符串分割为字符数组（支持Unicode）
 * @param {string} str - 要分割的字符串
 * @returns {string[]} 字符数组
 * @original function mnB(A) { return SY1(A) ? lP0(A) : fP0(A); }
 */
export function stringToArray(str) {
  if (hasComplexUnicode(str)) {
    // 处理复杂Unicode字符
    const UNICODE_REGEX = /[\ud83c[\udffb-\udfff](?=[\ud83c[\udffb-\udfff])|(?:[^\ud800-\udfff][\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]?|[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|[\ud800-\udbff][\udc00-\udfff]|[\ud800-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[^\ud800-\udfff])[\ufe0e\ufe0f]?(?:\u200d(?:[^\ud800-\udfff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff])[\ufe0e\ufe0f]?(?:[\u0300-\u036f\ufe20-\ufe2f\u20d0-\u20ff]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[^\ud800-\udfff])[\ufe0e\ufe0f]?)*/g;
    return str.match(UNICODE_REGEX) || [];
  }
  return str.split('');
}

/**
 * 首字母大写
 * @param {string} str - 要处理的字符串
 * @returns {string} 首字母大写的字符串
 * @original function lnB(A) { return nP0(hc(A).toLowerCase()); }
 */
export function capitalize(str) {
  const string = String(str);
  const chars = stringToArray(string.toLowerCase());
  const firstChar = chars[0];
  const restChars = chars.slice(1).join('');
  
  return firstChar ? firstChar.toUpperCase() + restChars : '';
}

/**
 * 转换为字符串
 * @param {*} value - 要转换的值
 * @returns {string} 转换后的字符串
 */
export function toString(value) {
  if (value == null) return '';
  if (typeof value === 'string') return value;
  if (Array.isArray(value)) return value.map(toString).join(',');
  if (typeof value === 'symbol') return value.toString();
  const result = String(value);
  return (result === '0' && (1 / value) === -Infinity) ? '-0' : result;
}
