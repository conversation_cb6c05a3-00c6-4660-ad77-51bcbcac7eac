/**
 * AbortController Polyfill
 * @description 为不支持AbortController的环境提供polyfill，对应第2670-2696行
 * @original 原始代码行 2670-2696
 */

/**
 * 警告日志函数
 * @param {string} message - 警告消息
 * @param {string} code - 错误代码
 * @param {string} errno - 错误号
 * @param {Function} fn - 相关函数
 * @original zt0函数
 */
function logWarning(message, code, errno, fn) {
  console.warn(`Warning [${code}]: ${message}`);
  if (fn && typeof fn === 'function') {
    console.warn(`Related function: ${fn.name || 'anonymous'}`);
  }
}

/**
 * AbortSignal Polyfill
 * @description 模拟AbortSignal的基本功能
 * @original Kt0类
 */
class AbortSignalPolyfill {
  constructor() {
    this.onabort = null;
    this._onabort = [];
    this.reason = undefined;
    this.aborted = false;
  }

  /**
   * 添加事件监听器
   * @param {string} type - 事件类型
   * @param {Function} listener - 监听器函数
   * @original addEventListener方法
   */
  addEventListener(type, listener) {
    if (type === 'abort' && typeof listener === 'function') {
      this._onabort.push(listener);
    }
  }

  /**
   * 移除事件监听器
   * @param {string} type - 事件类型
   * @param {Function} listener - 监听器函数
   */
  removeEventListener(type, listener) {
    if (type === 'abort') {
      const index = this._onabort.indexOf(listener);
      if (index > -1) {
        this._onabort.splice(index, 1);
      }
    }
  }

  /**
   * 分发事件
   * @param {Event} event - 事件对象
   */
  dispatchEvent(event) {
    if (event.type === 'abort') {
      this._triggerAbort(event.reason);
    }
  }

  /**
   * 触发中止事件
   * @param {*} reason - 中止原因
   * @private
   */
  _triggerAbort(reason) {
    if (this.aborted) return;
    
    this.reason = reason;
    this.aborted = true;
    
    // 调用所有监听器
    for (const listener of this._onabort) {
      try {
        listener({ type: 'abort', reason });
      } catch (error) {
        console.error('Error in abort listener:', error);
      }
    }
    
    // 调用onabort处理器
    if (typeof this.onabort === 'function') {
      try {
        this.onabort({ type: 'abort', reason });
      } catch (error) {
        console.error('Error in onabort handler:', error);
      }
    }
  }

  /**
   * 创建已中止的信号
   * @param {*} reason - 中止原因
   * @returns {AbortSignalPolyfill} 已中止的信号
   */
  static abort(reason) {
    const signal = new AbortSignalPolyfill();
    signal._triggerAbort(reason);
    return signal;
  }

  /**
   * 创建超时信号
   * @param {number} delay - 延迟时间（毫秒）
   * @returns {AbortSignalPolyfill} 超时信号
   */
  static timeout(delay) {
    const signal = new AbortSignalPolyfill();
    setTimeout(() => {
      signal._triggerAbort(new Error(`Operation timed out after ${delay}ms`));
    }, delay);
    return signal;
  }

  /**
   * 组合多个信号
   * @param {AbortSignal[]} signals - 信号数组
   * @returns {AbortSignalPolyfill} 组合信号
   */
  static any(signals) {
    const combinedSignal = new AbortSignalPolyfill();
    
    for (const signal of signals) {
      if (signal.aborted) {
        combinedSignal._triggerAbort(signal.reason);
        break;
      }
      
      signal.addEventListener('abort', () => {
        combinedSignal._triggerAbort(signal.reason);
      });
    }
    
    return combinedSignal;
  }
}

/**
 * AbortController Polyfill
 * @description 模拟AbortController的基本功能
 * @original cV1类
 */
class AbortControllerPolyfill {
  constructor() {
    // 显示警告（如果需要）
    showPolyfillWarning();
    
    this.signal = new AbortSignalPolyfill();
  }

  /**
   * 中止操作
   * @param {*} reason - 中止原因
   * @original abort方法
   */
  abort(reason) {
    if (this.signal.aborted) return;
    
    this.signal._triggerAbort(reason);
  }
}

/**
 * 警告状态管理
 * @original A变量和B函数
 */
let shouldShowWarning = process.env?.LRU_CACHE_IGNORE_AC_WARNING !== "1";

/**
 * 显示polyfill警告
 * @original B函数
 */
function showPolyfillWarning() {
  if (!shouldShowWarning) return;
  
  shouldShowWarning = false;
  logWarning(
    "AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.",
    "NO_ABORT_CONTROLLER",
    "ENOTSUP",
    showPolyfillWarning
  );
}

/**
 * 检查并设置全局AbortController
 * @description 如果全局环境中没有AbortController，则使用polyfill
 * @original 第2670-2696行的条件检查
 */
function setupAbortControllerPolyfill() {
  // 检查是否已经存在AbortController
  if (typeof globalThis.AbortController === "undefined") {
    // 设置polyfill
    globalThis.AbortSignal = AbortSignalPolyfill;
    globalThis.AbortController = AbortControllerPolyfill;
    
    // 也设置到global对象（Node.js环境）
    if (typeof global !== 'undefined') {
      global.AbortSignal = AbortSignalPolyfill;
      global.AbortController = AbortControllerPolyfill;
    }
  }
}

/**
 * 创建AbortController实例
 * @returns {AbortController|AbortControllerPolyfill} AbortController实例
 */
export function createAbortController() {
  if (typeof AbortController !== 'undefined') {
    return new AbortController();
  }
  return new AbortControllerPolyfill();
}

/**
 * 创建AbortSignal实例
 * @param {*} reason - 中止原因（可选）
 * @returns {AbortSignal|AbortSignalPolyfill} AbortSignal实例
 */
export function createAbortSignal(reason) {
  if (typeof AbortSignal !== 'undefined' && AbortSignal.abort) {
    return reason ? AbortSignal.abort(reason) : new AbortSignal();
  }
  return reason ? AbortSignalPolyfill.abort(reason) : new AbortSignalPolyfill();
}

/**
 * 创建超时AbortSignal
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {AbortSignal|AbortSignalPolyfill} 超时信号
 */
export function createTimeoutSignal(delay) {
  if (typeof AbortSignal !== 'undefined' && AbortSignal.timeout) {
    return AbortSignal.timeout(delay);
  }
  return AbortSignalPolyfill.timeout(delay);
}

/**
 * 组合多个AbortSignal
 * @param {AbortSignal[]} signals - 信号数组
 * @returns {AbortSignal|AbortSignalPolyfill} 组合信号
 */
export function combineAbortSignals(signals) {
  if (typeof AbortSignal !== 'undefined' && AbortSignal.any) {
    return AbortSignal.any(signals);
  }
  return AbortSignalPolyfill.any(signals);
}

/**
 * 检查值是否为AbortSignal
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为AbortSignal
 */
export function isAbortSignal(value) {
  return value instanceof AbortSignalPolyfill || 
         (typeof AbortSignal !== 'undefined' && value instanceof AbortSignal);
}

/**
 * 检查值是否为AbortController
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为AbortController
 */
export function isAbortController(value) {
  return value instanceof AbortControllerPolyfill ||
         (typeof AbortController !== 'undefined' && value instanceof AbortController);
}

/**
 * 等待AbortSignal中止
 * @param {AbortSignal} signal - 要等待的信号
 * @returns {Promise} 中止时resolve的Promise
 */
export function waitForAbort(signal) {
  if (signal.aborted) {
    return Promise.resolve(signal.reason);
  }
  
  return new Promise((resolve) => {
    signal.addEventListener('abort', () => {
      resolve(signal.reason);
    });
  });
}

/**
 * 创建可中止的Promise
 * @param {Function} executor - Promise执行器
 * @param {AbortSignal} signal - 中止信号
 * @returns {Promise} 可中止的Promise
 */
export function createAbortablePromise(executor, signal) {
  return new Promise((resolve, reject) => {
    if (signal.aborted) {
      reject(signal.reason || new Error('Operation was aborted'));
      return;
    }
    
    const abortHandler = () => {
      reject(signal.reason || new Error('Operation was aborted'));
    };
    
    signal.addEventListener('abort', abortHandler);
    
    executor(
      (value) => {
        signal.removeEventListener('abort', abortHandler);
        resolve(value);
      },
      (reason) => {
        signal.removeEventListener('abort', abortHandler);
        reject(reason);
      }
    );
  });
}

// 自动设置polyfill
setupAbortControllerPolyfill();

// 导出polyfill类
export { AbortControllerPolyfill, AbortSignalPolyfill };

// 导出设置函数
export { setupAbortControllerPolyfill, showPolyfillWarning };
