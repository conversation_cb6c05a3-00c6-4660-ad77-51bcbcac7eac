/**
 * Hash Map实现
 * @description 重构自原始文件中的Hash Map实现，对应第1396-1446行
 * @original 原始代码行 1396-1446
 */

/**
 * 获取Object.create的引用
 * @original var wiB = AV(Object, "create"), AO = wiB;
 */
const objectCreate = Object.create;

/**
 * Hash Map的undefined占位符
 * @original var NiB = "__lodash_hash_undefined__", SiB = "__lodash_hash_undefined__";
 */
const HASH_UNDEFINED = "__lodash_hash_undefined__";

/**
 * Object原型引用
 */
const objectProto = Object.prototype;
const hasOwnProperty = objectProto.hasOwnProperty;

/**
 * Hash Map构造函数
 * @param {Array} entries - 初始键值对数组
 * @original function vc(A) { var B = -1, Q = A == null ? 0 : A.length; this.clear(); while (++B < Q) { var D = A[B]; this.set(D[0], D[1]); } }
 */
export function Hash(entries) {
  let index = -1;
  const length = entries == null ? 0 : entries.length;
  
  this.clear();
  while (++index < length) {
    const entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}

/**
 * 清空Hash Map
 * @original function $iB() { this.__data__ = AO ? AO(null) : {}, this.size = 0; }
 */
function hashClear() {
  this.__data__ = objectCreate ? objectCreate(null) : {};
  this.size = 0;
}

/**
 * 删除Hash Map中的键值对
 * @param {string} key - 要删除的键
 * @returns {boolean} 是否删除成功
 * @original function qiB(A) { var B = this.has(A) && delete this.__data__[A]; return this.size -= B ? 1 : 0, B; }
 */
function hashDelete(key) {
  const result = this.has(key) && delete this.__data__[key];
  this.size -= result ? 1 : 0;
  return result;
}

/**
 * 获取Hash Map中指定键的值
 * @param {string} key - 要获取的键
 * @returns {*} 键对应的值
 * @original function RiB(A) { var B = this.__data__; if (AO) { var Q = B[A]; return Q === NiB ? void 0 : Q; } return MiB.call(B, A) ? B[A] : void 0; }
 */
function hashGet(key) {
  const data = this.__data__;
  
  if (objectCreate) {
    const result = data[key];
    return result === HASH_UNDEFINED ? undefined : result;
  }
  
  return hasOwnProperty.call(data, key) ? data[key] : undefined;
}

/**
 * 检查Hash Map中是否存在指定键
 * @param {string} key - 要检查的键
 * @returns {boolean} 是否存在该键
 * @original function PiB(A) { var B = this.__data__; return AO ? B[A] !== void 0 : TiB.call(B, A); }
 */
function hashHas(key) {
  const data = this.__data__;
  return objectCreate ? (data[key] !== undefined) : hasOwnProperty.call(data, key);
}

/**
 * 设置Hash Map中的键值对
 * @param {string} key - 要设置的键
 * @param {*} value - 要设置的值
 * @returns {Object} Hash Map实例
 * @original function jiB(A, B) { var Q = this.__data__; return this.size += this.has(A) ? 0 : 1, Q[A] = AO && B === void 0 ? SiB : B, this; }
 */
function hashSet(key, value) {
  const data = this.__data__;
  this.size += this.has(key) ? 0 : 1;
  data[key] = (objectCreate && value === undefined) ? HASH_UNDEFINED : value;
  return this;
}

// 设置Hash原型方法
Hash.prototype.clear = hashClear;
Hash.prototype.delete = hashDelete;
Hash.prototype.get = hashGet;
Hash.prototype.has = hashHas;
Hash.prototype.set = hashSet;

/**
 * 创建Hash Map实例
 * @param {Array} entries - 初始键值对数组
 * @returns {Hash} Hash Map实例
 */
export function createHash(entries) {
  return new Hash(entries);
}

/**
 * 检查值是否为Hash Map实例
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Hash Map实例
 */
export function isHash(value) {
  return value instanceof Hash;
}

/**
 * Hash Map工具函数
 */
export const HashUtils = {
  /**
   * 将对象转换为Hash Map
   * @param {Object} object - 要转换的对象
   * @returns {Hash} Hash Map实例
   */
  fromObject(object) {
    const entries = [];
    for (const key in object) {
      if (hasOwnProperty.call(object, key)) {
        entries.push([key, object[key]]);
      }
    }
    return new Hash(entries);
  },

  /**
   * 将Hash Map转换为普通对象
   * @param {Hash} hash - Hash Map实例
   * @returns {Object} 普通对象
   */
  toObject(hash) {
    if (!isHash(hash)) {
      throw new TypeError('Expected a Hash instance');
    }
    
    const result = {};
    const data = hash.__data__;
    
    for (const key in data) {
      if (objectCreate || hasOwnProperty.call(data, key)) {
        const value = data[key];
        result[key] = value === HASH_UNDEFINED ? undefined : value;
      }
    }
    
    return result;
  },

  /**
   * 获取Hash Map的所有键
   * @param {Hash} hash - Hash Map实例
   * @returns {Array} 键数组
   */
  keys(hash) {
    if (!isHash(hash)) {
      throw new TypeError('Expected a Hash instance');
    }
    
    const keys = [];
    const data = hash.__data__;
    
    for (const key in data) {
      if (objectCreate || hasOwnProperty.call(data, key)) {
        keys.push(key);
      }
    }
    
    return keys;
  },

  /**
   * 获取Hash Map的所有值
   * @param {Hash} hash - Hash Map实例
   * @returns {Array} 值数组
   */
  values(hash) {
    if (!isHash(hash)) {
      throw new TypeError('Expected a Hash instance');
    }
    
    const values = [];
    const data = hash.__data__;
    
    for (const key in data) {
      if (objectCreate || hasOwnProperty.call(data, key)) {
        const value = data[key];
        values.push(value === HASH_UNDEFINED ? undefined : value);
      }
    }
    
    return values;
  },

  /**
   * 获取Hash Map的所有键值对
   * @param {Hash} hash - Hash Map实例
   * @returns {Array} 键值对数组
   */
  entries(hash) {
    if (!isHash(hash)) {
      throw new TypeError('Expected a Hash instance');
    }
    
    const entries = [];
    const data = hash.__data__;
    
    for (const key in data) {
      if (objectCreate || hasOwnProperty.call(data, key)) {
        const value = data[key];
        entries.push([key, value === HASH_UNDEFINED ? undefined : value]);
      }
    }
    
    return entries;
  },

  /**
   * 遍历Hash Map
   * @param {Hash} hash - Hash Map实例
   * @param {Function} iteratee - 遍历函数
   */
  forEach(hash, iteratee) {
    if (!isHash(hash)) {
      throw new TypeError('Expected a Hash instance');
    }
    
    const data = hash.__data__;
    
    for (const key in data) {
      if (objectCreate || hasOwnProperty.call(data, key)) {
        const value = data[key];
        const actualValue = value === HASH_UNDEFINED ? undefined : value;
        
        if (iteratee(actualValue, key, hash) === false) {
          break;
        }
      }
    }
  },

  /**
   * 克隆Hash Map
   * @param {Hash} hash - 要克隆的Hash Map实例
   * @returns {Hash} 克隆的Hash Map实例
   */
  clone(hash) {
    if (!isHash(hash)) {
      throw new TypeError('Expected a Hash instance');
    }
    
    return new Hash(this.entries(hash));
  },

  /**
   * 合并多个Hash Map
   * @param {...Hash} hashes - 要合并的Hash Map实例
   * @returns {Hash} 合并后的Hash Map实例
   */
  merge(...hashes) {
    const result = new Hash();
    
    for (const hash of hashes) {
      if (isHash(hash)) {
        this.forEach(hash, (value, key) => {
          result.set(key, value);
        });
      }
    }
    
    return result;
  }
};

// 导出Hash构造函数作为默认导出
export default Hash;
