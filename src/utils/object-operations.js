/**
 * 对象操作工具函数
 * @description 重构自原始文件中的对象操作函数，对应第1814-1850行
 * @original 原始代码行 1814-1850
 */

import { last } from './collection-operations.js';

/**
 * 删除对象中指定路径的属性
 * @param {Object} object - 源对象
 * @param {string|Array} path - 要删除的路径
 * @returns {boolean} 是否删除成功
 * @original function AoB(A, B) { return B = bH(B, A), A = Wj0(A, B), A == null || delete A[fH(BI(B))]; }
 */
export function unset(object, path) {
  path = castPath(path, object);
  object = parent(object, path);
  return object == null || delete object[toKey(last(path))];
}

/**
 * 自定义克隆函数，用于处理特殊值
 * @param {*} value - 要处理的值
 * @returns {*} 处理后的值
 * @original function BoB(A) { return Af(A) ? void 0 : A; }
 */
export function customizer(value) {
  return isUndefined(value) ? undefined : value;
}

/**
 * 克隆标志常量
 * @original var QoB = 1, DoB = 2, ZoB = 4;
 */
const CLONE_DEEP_FLAG = 1;
const CLONE_FLAT_FLAG = 2;
const CLONE_SYMBOLS_FLAG = 4;

/**
 * 省略对象中的指定属性
 * @param {Object} object - 源对象
 * @param {...(string|string[])} paths - 要省略的路径
 * @returns {Object} 省略指定属性后的新对象
 * @original var GoB = TY1(function (A, B) { ... });
 */
export const omit = function(object, ...paths) {
  const result = {};
  if (object == null) return result;
  
  let isDeep = false;
  
  // 处理路径参数
  paths = paths.map(function(path) {
    path = castPath(path, object);
    isDeep || (isDeep = path.length > 1);
    return path;
  });
  
  // 复制所有属性
  copyObject(object, getAllKeys(object), result);
  
  // 如果有深层路径，进行深度克隆
  if (isDeep) {
    result = baseClone(result, CLONE_DEEP_FLAG | CLONE_FLAT_FLAG | CLONE_SYMBOLS_FLAG, customizer);
  }
  
  // 删除指定的路径
  let index = paths.length;
  while (index--) {
    unset(result, paths[index]);
  }
  
  return result;
};

/**
 * 在对象的指定路径设置值
 * @param {Object} object - 目标对象
 * @param {string|Array} path - 路径
 * @param {*} value - 要设置的值
 * @param {Function} customizer - 自定义函数
 * @returns {Object} 目标对象
 * @original function FoB(A, B, Q, D) { if (!T3(A)) return A; ... }
 */
export function baseSet(object, path, value, customizer) {
  if (!isObject(object)) return object;
  
  path = castPath(path, object);
  
  let index = -1;
  const length = path.length;
  const lastIndex = length - 1;
  let nested = object;
  
  while (nested != null && ++index < length) {
    const key = toKey(path[index]);
    let newValue = value;
    
    if (key === "__proto__" || key === "constructor" || key === "prototype") {
      return object;
    }
    
    if (index != lastIndex) {
      const objValue = nested[key];
      newValue = customizer ? customizer(objValue, key, nested) : undefined;
      if (newValue === undefined) {
        newValue = isObject(objValue) ? objValue : (isIndex(path[index + 1]) ? [] : {});
      }
    }
    
    assignValue(nested, key, newValue);
    nested = nested[key];
  }
  
  return object;
}

/**
 * 设置对象的值
 * @param {Object} object - 目标对象
 * @param {string|Array} path - 路径
 * @param {*} value - 要设置的值
 * @returns {Object} 目标对象
 */
export function set(object, path, value) {
  return object == null ? object : baseSet(object, path, value);
}

/**
 * 使用自定义函数设置对象的值
 * @param {Object} object - 目标对象
 * @param {string|Array} path - 路径
 * @param {*} value - 要设置的值
 * @param {Function} customizer - 自定义函数
 * @returns {Object} 目标对象
 */
export function setWith(object, path, value, customizer) {
  customizer = typeof customizer == 'function' ? customizer : undefined;
  return object == null ? object : baseSet(object, path, value, customizer);
}

// 辅助函数

/**
 * 将路径转换为路径数组
 * @param {*} value - 路径值
 * @param {Object} object - 对象上下文
 * @returns {Array} 路径数组
 */
function castPath(value, object) {
  if (Array.isArray(value)) {
    return value;
  }
  
  return isKey(value, object) ? [value] : stringToPath(value);
}

/**
 * 获取对象的父对象
 * @param {Object} object - 源对象
 * @param {Array} path - 路径数组
 * @returns {*} 父对象
 */
function parent(object, path) {
  return path.length < 2 ? object : baseGet(object, baseSlice(path, 0, -1));
}

/**
 * 将值转换为属性键
 * @param {*} value - 要转换的值
 * @returns {string|symbol} 属性键
 */
function toKey(value) {
  if (typeof value == 'string' || isSymbol(value)) {
    return value;
  }
  const result = (value + '');
  return (result == '0' && (1 / value) == -Infinity) ? '-0' : result;
}

/**
 * 检查值是否为undefined
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为undefined
 */
function isUndefined(value) {
  return value === undefined;
}

/**
 * 检查值是否为对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为对象
 */
function isObject(value) {
  const type = typeof value;
  return value != null && (type == 'object' || type == 'function');
}

/**
 * 检查值是否为Symbol
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Symbol
 */
function isSymbol(value) {
  return typeof value == 'symbol' ||
    (isObjectLike(value) && Object.prototype.toString.call(value) == '[object Symbol]');
}

/**
 * 检查值是否为类对象
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为类对象
 */
function isObjectLike(value) {
  return value != null && typeof value == 'object';
}

/**
 * 检查值是否为有效的数组索引
 * @param {*} value - 要检查的值
 * @param {number} length - 数组长度
 * @returns {boolean} 是否为有效索引
 */
function isIndex(value, length) {
  const type = typeof value;
  length = length == null ? Number.MAX_SAFE_INTEGER : length;
  
  return !!length &&
    (type == 'number' ||
      (type != 'symbol' && /^(?:0|[1-9]\d*)$/.test(value))) &&
    (value > -1 && value % 1 == 0 && value < length);
}

/**
 * 检查值是否为属性名
 * @param {*} value - 要检查的值
 * @param {Object} object - 对象上下文
 * @returns {boolean} 是否为属性名
 */
function isKey(value, object) {
  if (Array.isArray(value)) {
    return false;
  }
  const type = typeof value;
  if (type == 'number' || type == 'symbol' || type == 'boolean' ||
      value == null || isSymbol(value)) {
    return true;
  }
  return /^(?:[a-zA-Z_$][a-zA-Z0-9_$]*|\d+)$/.test(value) ||
    !/^(?:\.|\.\.|\[|\]|\\)/.test(value) ||
    (object != null && value in Object(object));
}

/**
 * 将字符串转换为路径数组
 * @param {string} string - 路径字符串
 * @returns {Array} 路径数组
 */
function stringToPath(string) {
  const result = [];
  if (string.charCodeAt(0) === 46 /* . */) {
    result.push('');
  }
  string.replace(/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*)?\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g, function(match, number, quote, subString) {
    result.push(quote ? subString.replace(/\\(\\)?/g, '$1') : (number || match));
  });
  return result;
}

/**
 * 根据路径获取对象的值
 * @param {Object} object - 源对象
 * @param {Array} path - 路径数组
 * @returns {*} 获取的值
 */
function baseGet(object, path) {
  path = castPath(path, object);
  
  let index = 0;
  const length = path.length;
  
  while (object != null && index < length) {
    object = object[toKey(path[index++])];
  }
  
  return (index && index == length) ? object : undefined;
}

/**
 * 数组切片函数
 * @param {Array} array - 要切片的数组
 * @param {number} start - 开始索引
 * @param {number} end - 结束索引
 * @returns {Array} 切片后的数组
 */
function baseSlice(array, start, end) {
  let index = -1;
  let length = array.length;
  
  if (start < 0) {
    start = -start > length ? 0 : (length + start);
  }
  end = end > length ? length : end;
  if (end < 0) {
    end += length;
  }
  length = start > end ? 0 : ((end - start) >>> 0);
  start >>>= 0;
  
  const result = Array(length);
  while (++index < length) {
    result[index] = array[index + start];
  }
  return result;
}

/**
 * 复制对象属性
 * @param {Object} source - 源对象
 * @param {Array} props - 属性数组
 * @param {Object} object - 目标对象
 * @returns {Object} 目标对象
 */
function copyObject(source, props, object) {
  let index = -1;
  const length = props.length;
  
  while (++index < length) {
    const key = props[index];
    object[key] = source[key];
  }
  return object;
}

/**
 * 获取对象的所有键（包括不可枚举的）
 * @param {Object} object - 源对象
 * @returns {Array} 所有键的数组
 */
function getAllKeys(object) {
  return Object.getOwnPropertyNames(object);
}

/**
 * 基础克隆函数
 * @param {*} value - 要克隆的值
 * @param {number} bitmask - 克隆标志
 * @param {Function} customizer - 自定义函数
 * @returns {*} 克隆的值
 */
function baseClone(value, bitmask, customizer) {
  // 简化的克隆实现
  if (customizer) {
    const result = customizer(value);
    if (result !== undefined) {
      return result;
    }
  }
  
  if (value == null || typeof value != 'object') {
    return value;
  }
  
  if (Array.isArray(value)) {
    return value.map(item => baseClone(item, bitmask, customizer));
  }
  
  const result = {};
  for (const key in value) {
    if (Object.prototype.hasOwnProperty.call(value, key)) {
      result[key] = baseClone(value[key], bitmask, customizer);
    }
  }
  
  return result;
}

/**
 * 分配值给对象属性
 * @param {Object} object - 目标对象
 * @param {string} key - 属性键
 * @param {*} value - 属性值
 */
function assignValue(object, key, value) {
  const objValue = object[key];
  if (!(Object.prototype.hasOwnProperty.call(object, key) && eq(objValue, value)) ||
      (value === undefined && !(key in object))) {
    object[key] = value;
  }
}

/**
 * 执行SameValueZero比较
 * @param {*} value - 要比较的值
 * @param {*} other - 另一个值
 * @returns {boolean} 是否相等
 */
function eq(value, other) {
  return value === other || (value !== value && other !== other);
}
