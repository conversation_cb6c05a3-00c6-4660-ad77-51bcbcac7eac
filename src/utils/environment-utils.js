/**
 * 环境变量和配置工具
 * @description 重构自原始文件中的环境变量处理函数，对应第2367-2412行
 * @original 原始代码行 2367-2412
 */

/**
 * 检查布尔值环境变量
 * @param {string} value - 环境变量值
 * @returns {boolean} 是否为false值
 * @original function Uo0(A) { ... }
 */
export function isFalsyEnvValue(value) {
  if (!value) return false;
  
  const normalizedValue = value.toLowerCase().trim();
  return ["0", "false", "no", "off"].includes(normalizedValue);
}

/**
 * 检查布尔值环境变量（取反）
 * @param {string} value - 环境变量值
 * @returns {boolean} 是否为true值
 * @original gQ函数的实现（推测）
 */
export function isTruthyEnvValue(value) {
  if (!value) return false;
  
  const normalizedValue = value.toLowerCase().trim();
  return !["0", "false", "no", "off"].includes(normalizedValue);
}

/**
 * 解析环境变量数组
 * @param {Array} envArray - 环境变量数组，格式为 ["KEY1=value1", "KEY2=value2"]
 * @returns {Object} 解析后的环境变量对象
 * @original function wo0(A) { ... }
 */
export function parseEnvironmentVariables(envArray) {
  const result = {};
  
  if (envArray) {
    for (const envVar of envArray) {
      const [key, ...valueParts] = envVar.split("=");
      
      if (!key || valueParts.length === 0) {
        throw new Error(
          `Invalid environment variable format: ${envVar}, ` +
          `environment variables should be added as: -e KEY1=value1 -e KEY2=value2`
        );
      }
      
      result[key] = valueParts.join("=");
    }
  }
  
  return result;
}

/**
 * 获取云ML区域
 * @returns {string} 云ML区域
 * @original function $O() { return process.env.CLOUD_ML_REGION || "us-east5"; }
 */
export function getCloudMLRegion() {
  return process.env.CLOUD_ML_REGION || "us-east5";
}

/**
 * 检查是否维护项目工作目录
 * @returns {boolean} 是否维护项目工作目录
 * @original function Ni1() { return gQ(process.env.CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR); }
 */
export function shouldMaintainProjectWorkingDir() {
  return isTruthyEnvValue(process.env.CLAUDE_BASH_MAINTAIN_PROJECT_WORKING_DIR);
}

/**
 * 根据模型获取Vertex区域
 * @param {string} model - 模型名称
 * @returns {string} Vertex区域
 * @original function $o0(A) { ... }
 */
export function getVertexRegionForModel(model) {
  if (model?.startsWith("claude-3-5-haiku")) {
    return process.env.VERTEX_REGION_CLAUDE_3_5_HAIKU || getCloudMLRegion();
  }
  
  if (model?.startsWith("claude-3-5-sonnet")) {
    return process.env.VERTEX_REGION_CLAUDE_3_5_SONNET || getCloudMLRegion();
  }
  
  if (model?.startsWith("claude-3-7-sonnet")) {
    return process.env.VERTEX_REGION_CLAUDE_3_7_SONNET || getCloudMLRegion();
  }
  
  if (model?.startsWith("claude-opus-4-1")) {
    return process.env.VERTEX_REGION_CLAUDE_4_1_OPUS || getCloudMLRegion();
  }
  
  if (model?.startsWith("claude-opus-4")) {
    return process.env.VERTEX_REGION_CLAUDE_4_0_OPUS || getCloudMLRegion();
  }
  
  if (model?.startsWith("claude-sonnet-4")) {
    return process.env.VERTEX_REGION_CLAUDE_4_0_SONNET || getCloudMLRegion();
  }
  
  return getCloudMLRegion();
}

/**
 * 上下文管理版本
 * @original var Mo0 = "context-management-2025-06-27";
 */
export const CONTEXT_MANAGEMENT_VERSION = "context-management-2025-06-27";

/**
 * 本地OAuth配置
 * @description 本地开发环境的OAuth配置
 * @original p_9对象的定义
 */
export const LOCAL_OAUTH_CONFIG = {
  BASE_API_URL: "http://localhost:3000",
  CONSOLE_AUTHORIZE_URL: "http://localhost:3000/oauth/authorize",
  CLAUDE_AI_AUTHORIZE_URL: "http://localhost:4000/oauth/authorize",
  TOKEN_URL: "http://localhost:3000/v1/oauth/token",
  API_KEY_URL: "http://localhost:3000/api/oauth/claude_cli/create_api_key",
  ROLES_URL: "http://localhost:3000/api/oauth/claude_cli/roles",
  CONSOLE_SUCCESS_URL: "http://localhost:3000/buy_credits?returnUrl=/oauth/code/success%3Fapp%3Dclaude-code",
  CLAUDEAI_SUCCESS_URL: "http://localhost:3000/oauth/code/success?app=claude-code",
  MANUAL_REDIRECT_URL: "https://console.staging.ant.dev/oauth/code/callback",
  CLIENT_ID: "22422756-60c9-4084-8eb7-27705fd5cf9a"
};

/**
 * 生产环境OAuth配置
 * @description 生产环境的OAuth配置（需要从其他地方获取）
 * @original l_9对象的引用
 */
export const PRODUCTION_OAUTH_CONFIG = {
  // @todo: 需要从原始代码中找到l_9的定义
  BASE_API_URL: "https://api.claude.ai",
  CONSOLE_AUTHORIZE_URL: "https://console.anthropic.com/oauth/authorize",
  CLAUDE_AI_AUTHORIZE_URL: "https://claude.ai/oauth/authorize",
  TOKEN_URL: "https://api.claude.ai/v1/oauth/token",
  API_KEY_URL: "https://api.claude.ai/api/oauth/claude_cli/create_api_key",
  ROLES_URL: "https://api.claude.ai/api/oauth/claude_cli/roles",
  CONSOLE_SUCCESS_URL: "https://console.anthropic.com/buy_credits?returnUrl=/oauth/code/success%3Fapp%3Dclaude-code",
  CLAUDEAI_SUCCESS_URL: "https://claude.ai/oauth/code/success?app=claude-code",
  MANUAL_REDIRECT_URL: "https://console.anthropic.com/oauth/code/callback",
  CLIENT_ID: "production-client-id" // @todo: 需要实际的生产环境客户端ID
};

/**
 * 获取OAuth配置
 * @returns {Object|false} OAuth配置对象或false
 * @original function P5() { return process.env.USE_LOCAL_OAUTH === "1" && p_9 || !1 || l_9; }
 */
export function getOAuthConfig() {
  if (process.env.USE_LOCAL_OAUTH === "1") {
    return LOCAL_OAUTH_CONFIG;
  }
  
  // 在生产环境中返回生产配置
  return PRODUCTION_OAUTH_CONFIG;
}

/**
 * 检查是否使用本地OAuth
 * @returns {boolean} 是否使用本地OAuth
 */
export function isUsingLocalOAuth() {
  return process.env.USE_LOCAL_OAUTH === "1";
}

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
export function getApiBaseUrl() {
  const config = getOAuthConfig();
  return config ? config.BASE_API_URL : "https://api.claude.ai";
}

/**
 * 获取控制台授权URL
 * @returns {string} 控制台授权URL
 */
export function getConsoleAuthorizeUrl() {
  const config = getOAuthConfig();
  return config ? config.CONSOLE_AUTHORIZE_URL : "https://console.anthropic.com/oauth/authorize";
}

/**
 * 获取Claude AI授权URL
 * @returns {string} Claude AI授权URL
 */
export function getClaudeAIAuthorizeUrl() {
  const config = getOAuthConfig();
  return config ? config.CLAUDE_AI_AUTHORIZE_URL : "https://claude.ai/oauth/authorize";
}

/**
 * 获取令牌URL
 * @returns {string} 令牌URL
 */
export function getTokenUrl() {
  const config = getOAuthConfig();
  return config ? config.TOKEN_URL : "https://api.claude.ai/v1/oauth/token";
}

/**
 * 获取API密钥URL
 * @returns {string} API密钥URL
 */
export function getApiKeyUrl() {
  const config = getOAuthConfig();
  return config ? config.API_KEY_URL : "https://api.claude.ai/api/oauth/claude_cli/create_api_key";
}

/**
 * 获取角色URL
 * @returns {string} 角色URL
 */
export function getRolesUrl() {
  const config = getOAuthConfig();
  return config ? config.ROLES_URL : "https://api.claude.ai/api/oauth/claude_cli/roles";
}

/**
 * 获取客户端ID
 * @returns {string} 客户端ID
 */
export function getClientId() {
  const config = getOAuthConfig();
  return config ? config.CLIENT_ID : "production-client-id";
}

/**
 * 环境变量辅助函数
 */
export const EnvUtils = {
  /**
   * 获取环境变量，支持默认值
   * @param {string} key - 环境变量键
   * @param {*} defaultValue - 默认值
   * @returns {*} 环境变量值或默认值
   */
  get(key, defaultValue = undefined) {
    return process.env[key] || defaultValue;
  },

  /**
   * 获取布尔值环境变量
   * @param {string} key - 环境变量键
   * @param {boolean} defaultValue - 默认值
   * @returns {boolean} 布尔值
   */
  getBoolean(key, defaultValue = false) {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    return isTruthyEnvValue(value);
  },

  /**
   * 获取数字环境变量
   * @param {string} key - 环境变量键
   * @param {number} defaultValue - 默认值
   * @returns {number} 数字值
   */
  getNumber(key, defaultValue = 0) {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? defaultValue : parsed;
  },

  /**
   * 获取浮点数环境变量
   * @param {string} key - 环境变量键
   * @param {number} defaultValue - 默认值
   * @returns {number} 浮点数值
   */
  getFloat(key, defaultValue = 0.0) {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    const parsed = parseFloat(value);
    return isNaN(parsed) ? defaultValue : parsed;
  },

  /**
   * 获取JSON环境变量
   * @param {string} key - 环境变量键
   * @param {*} defaultValue - 默认值
   * @returns {*} 解析后的JSON值或默认值
   */
  getJSON(key, defaultValue = null) {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    
    try {
      return JSON.parse(value);
    } catch {
      return defaultValue;
    }
  },

  /**
   * 获取数组环境变量（逗号分隔）
   * @param {string} key - 环境变量键
   * @param {Array} defaultValue - 默认值
   * @returns {Array} 数组值
   */
  getArray(key, defaultValue = []) {
    const value = process.env[key];
    if (value === undefined) return defaultValue;
    return value.split(',').map(item => item.trim()).filter(item => item.length > 0);
  },

  /**
   * 检查环境变量是否存在
   * @param {string} key - 环境变量键
   * @returns {boolean} 是否存在
   */
  has(key) {
    return key in process.env;
  },

  /**
   * 设置环境变量
   * @param {string} key - 环境变量键
   * @param {string} value - 环境变量值
   */
  set(key, value) {
    process.env[key] = String(value);
  },

  /**
   * 删除环境变量
   * @param {string} key - 环境变量键
   */
  delete(key) {
    delete process.env[key];
  }
};
