/**
 * 字符编码工具函数
 * @description 重构自原始文件中的字符编码处理代码，对应第3898-3900行
 * @original 原始代码行 3898-3900
 */

/**
 * 字符编码枚举
 * @description 定义各种字符的ASCII码值
 * @original lIA枚举
 */
export const CharacterCodes = {
  // 控制字符
  lineFeed: 10,           // \n
  carriageReturn: 13,     // \r
  space: 32,              // 空格
  tab: 9,                 // \t
  formFeed: 12,           // \f

  // 数字字符 (0-9)
  _0: 48,
  _1: 49,
  _2: 50,
  _3: 51,
  _4: 52,
  _5: 53,
  _6: 54,
  _7: 55,
  _8: 56,
  _9: 57,

  // 小写字母 (a-z)
  a: 97,
  b: 98,
  c: 99,
  d: 100,
  e: 101,
  f: 102,
  g: 103,
  h: 104,
  i: 105,
  j: 106,
  k: 107,
  l: 108,
  m: 109,
  n: 110,
  o: 111,
  p: 112,
  q: 113,
  r: 114,
  s: 115,
  t: 116,
  u: 117,
  v: 118,
  w: 119,
  x: 120,
  y: 121,
  z: 122,

  // 大写字母 (A-Z)
  A: 65,
  B: 66,
  C: 67,
  D: 68,
  E: 69,
  F: 70,
  G: 71,
  H: 72,
  I: 73,
  J: 74,
  K: 75,
  L: 76,
  M: 77,
  N: 78,
  O: 79,
  P: 80,
  Q: 81,
  R: 82,
  S: 83,
  T: 84,
  U: 85,
  V: 86,
  W: 87,
  X: 88,
  Y: 89,
  Z: 90,

  // 特殊字符
  asterisk: 42,           // *
  backslash: 92,          // \
  closeBrace: 125,        // }
  closeBracket: 93,       // ]
  colon: 58,              // :
  comma: 44,              // ,
  dot: 46,                // .
  doubleQuote: 34,        // "
  minus: 45,              // -
  openBrace: 123,         // {
  openBracket: 91,        // [
  plus: 43,               // +
  slash: 47               // /
};

/**
 * 字符编码工具类
 * @description 提供字符编码相关的工具函数
 */
export class CharacterEncodingUtils {
  /**
   * 检查字符是否为数字
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为数字字符
   */
  static isDigit(charCode) {
    return charCode >= CharacterCodes._0 && charCode <= CharacterCodes._9;
  }

  /**
   * 检查字符是否为小写字母
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为小写字母
   */
  static isLowerCase(charCode) {
    return charCode >= CharacterCodes.a && charCode <= CharacterCodes.z;
  }

  /**
   * 检查字符是否为大写字母
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为大写字母
   */
  static isUpperCase(charCode) {
    return charCode >= CharacterCodes.A && charCode <= CharacterCodes.Z;
  }

  /**
   * 检查字符是否为字母
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为字母
   */
  static isLetter(charCode) {
    return this.isLowerCase(charCode) || this.isUpperCase(charCode);
  }

  /**
   * 检查字符是否为字母或数字
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为字母或数字
   */
  static isAlphaNumeric(charCode) {
    return this.isLetter(charCode) || this.isDigit(charCode);
  }

  /**
   * 检查字符是否为十六进制数字
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为十六进制数字
   */
  static isHexDigit(charCode) {
    return this.isDigit(charCode) || 
           (charCode >= CharacterCodes.a && charCode <= CharacterCodes.f) ||
           (charCode >= CharacterCodes.A && charCode <= CharacterCodes.F);
  }

  /**
   * 检查字符是否为空白字符
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为空白字符
   */
  static isWhitespace(charCode) {
    return charCode === CharacterCodes.space ||
           charCode === CharacterCodes.tab ||
           charCode === CharacterCodes.lineFeed ||
           charCode === CharacterCodes.carriageReturn ||
           charCode === CharacterCodes.formFeed;
  }

  /**
   * 检查字符是否为换行符
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为换行符
   */
  static isLineBreak(charCode) {
    return charCode === CharacterCodes.lineFeed || 
           charCode === CharacterCodes.carriageReturn;
  }

  /**
   * 将字符编码转换为字符
   * @param {number} charCode - 字符编码
   * @returns {string} 字符
   */
  static fromCharCode(charCode) {
    return String.fromCharCode(charCode);
  }

  /**
   * 将字符转换为字符编码
   * @param {string} char - 字符
   * @returns {number} 字符编码
   */
  static toCharCode(char) {
    return char.charCodeAt(0);
  }

  /**
   * 将字符串转换为字符编码数组
   * @param {string} str - 字符串
   * @returns {number[]} 字符编码数组
   */
  static stringToCharCodes(str) {
    const codes = [];
    for (let i = 0; i < str.length; i++) {
      codes.push(str.charCodeAt(i));
    }
    return codes;
  }

  /**
   * 将字符编码数组转换为字符串
   * @param {number[]} charCodes - 字符编码数组
   * @returns {string} 字符串
   */
  static charCodesToString(charCodes) {
    return String.fromCharCode(...charCodes);
  }

  /**
   * 转换为小写字符编码
   * @param {number} charCode - 字符编码
   * @returns {number} 小写字符编码
   */
  static toLowerCase(charCode) {
    if (this.isUpperCase(charCode)) {
      return charCode + 32; // A-Z 到 a-z 的差值是32
    }
    return charCode;
  }

  /**
   * 转换为大写字符编码
   * @param {number} charCode - 字符编码
   * @returns {number} 大写字符编码
   */
  static toUpperCase(charCode) {
    if (this.isLowerCase(charCode)) {
      return charCode - 32; // a-z 到 A-Z 的差值是32
    }
    return charCode;
  }

  /**
   * 获取字符的类型描述
   * @param {number} charCode - 字符编码
   * @returns {string} 字符类型描述
   */
  static getCharacterType(charCode) {
    if (this.isDigit(charCode)) return 'digit';
    if (this.isLowerCase(charCode)) return 'lowercase';
    if (this.isUpperCase(charCode)) return 'uppercase';
    if (this.isWhitespace(charCode)) return 'whitespace';
    if (this.isLineBreak(charCode)) return 'linebreak';
    return 'symbol';
  }

  /**
   * 检查字符是否为标识符起始字符
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为标识符起始字符
   */
  static isIdentifierStart(charCode) {
    return this.isLetter(charCode) || charCode === CharacterCodes.underscore;
  }

  /**
   * 检查字符是否为标识符字符
   * @param {number} charCode - 字符编码
   * @returns {boolean} 是否为标识符字符
   */
  static isIdentifierPart(charCode) {
    return this.isAlphaNumeric(charCode) || charCode === CharacterCodes.underscore;
  }

  /**
   * 转义特殊字符
   * @param {number} charCode - 字符编码
   * @returns {string} 转义后的字符串
   */
  static escapeCharacter(charCode) {
    switch (charCode) {
      case CharacterCodes.lineFeed:
        return '\\n';
      case CharacterCodes.carriageReturn:
        return '\\r';
      case CharacterCodes.tab:
        return '\\t';
      case CharacterCodes.formFeed:
        return '\\f';
      case CharacterCodes.backslash:
        return '\\\\';
      case CharacterCodes.doubleQuote:
        return '\\"';
      default:
        return String.fromCharCode(charCode);
    }
  }

  /**
   * 解析转义字符
   * @param {string} escapedChar - 转义字符串
   * @returns {number} 字符编码
   */
  static parseEscapedCharacter(escapedChar) {
    switch (escapedChar) {
      case '\\n':
        return CharacterCodes.lineFeed;
      case '\\r':
        return CharacterCodes.carriageReturn;
      case '\\t':
        return CharacterCodes.tab;
      case '\\f':
        return CharacterCodes.formFeed;
      case '\\\\':
        return CharacterCodes.backslash;
      case '\\"':
        return CharacterCodes.doubleQuote;
      default:
        return escapedChar.charCodeAt(0);
    }
  }
}

/**
 * 字符编码常量
 */
export const CHAR_CODES = CharacterCodes;

/**
 * 便捷函数：检查字符是否为数字
 * @param {string} char - 字符
 * @returns {boolean} 是否为数字字符
 */
export function isDigitChar(char) {
  return CharacterEncodingUtils.isDigit(char.charCodeAt(0));
}

/**
 * 便捷函数：检查字符是否为字母
 * @param {string} char - 字符
 * @returns {boolean} 是否为字母
 */
export function isLetterChar(char) {
  return CharacterEncodingUtils.isLetter(char.charCodeAt(0));
}

/**
 * 便捷函数：检查字符是否为空白字符
 * @param {string} char - 字符
 * @returns {boolean} 是否为空白字符
 */
export function isWhitespaceChar(char) {
  return CharacterEncodingUtils.isWhitespace(char.charCodeAt(0));
}

/**
 * 便捷函数：检查字符是否为换行符
 * @param {string} char - 字符
 * @returns {boolean} 是否为换行符
 */
export function isLineBreakChar(char) {
  return CharacterEncodingUtils.isLineBreak(char.charCodeAt(0));
}

// 添加下划线字符编码（在原始代码中可能被遗漏）
CharacterCodes.underscore = 95; // _
