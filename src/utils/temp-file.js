/**
 * 临时文件工具函数
 * @description 提供临时文件创建和管理功能
 */

import { join } from 'path';
import { tmpdir } from 'os';
import { writeFileSync, unlinkSync, existsSync } from 'fs';

/**
 * 生成临时文件路径
 * @param {string} prefix - 文件名前缀
 * @param {string} suffix - 文件扩展名
 * @returns {string} 临时文件路径
 * @original let { generateTempFilePath: E1 } = await Promise.resolve().then(() => (uO0(), omB));
 */
export function generateTempFilePath(prefix = 'temp', suffix = '') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const fileName = `${prefix}-${timestamp}-${random}${suffix}`;
  return join(tmpdir(), fileName);
}

/**
 * 创建临时文件
 * @param {string} content - 文件内容
 * @param {string} prefix - 文件名前缀
 * @param {string} suffix - 文件扩展名
 * @returns {string} 临时文件路径
 */
export function createTempFile(content, prefix = 'temp', suffix = '') {
  const filePath = generateTempFilePath(prefix, suffix);
  writeFileSync(filePath, content, 'utf8');
  return filePath;
}

/**
 * 删除临时文件
 * @param {string} filePath - 文件路径
 * @returns {boolean} 删除是否成功
 */
export function deleteTempFile(filePath) {
  try {
    if (existsSync(filePath)) {
      unlinkSync(filePath);
      return true;
    }
    return false;
  } catch {
    return false;
  }
}

/**
 * 临时文件管理器类
 */
export class TempFileManager {
  constructor() {
    this.tempFiles = new Set();
  }

  /**
   * 创建临时文件并跟踪
   * @param {string} content - 文件内容
   * @param {string} prefix - 文件名前缀
   * @param {string} suffix - 文件扩展名
   * @returns {string} 临时文件路径
   */
  createTempFile(content, prefix = 'temp', suffix = '') {
    const filePath = createTempFile(content, prefix, suffix);
    this.tempFiles.add(filePath);
    return filePath;
  }

  /**
   * 删除指定的临时文件
   * @param {string} filePath - 文件路径
   * @returns {boolean} 删除是否成功
   */
  deleteTempFile(filePath) {
    const success = deleteTempFile(filePath);
    if (success) {
      this.tempFiles.delete(filePath);
    }
    return success;
  }

  /**
   * 清理所有临时文件
   * @returns {number} 成功删除的文件数量
   */
  cleanup() {
    let deletedCount = 0;
    for (const filePath of this.tempFiles) {
      if (deleteTempFile(filePath)) {
        deletedCount++;
      }
    }
    this.tempFiles.clear();
    return deletedCount;
  }

  /**
   * 获取当前跟踪的临时文件列表
   * @returns {string[]} 临时文件路径列表
   */
  getTempFiles() {
    return Array.from(this.tempFiles);
  }
}

// 全局临时文件管理器实例
const globalTempFileManager = new TempFileManager();

/**
 * 获取全局临时文件管理器
 * @returns {TempFileManager} 全局临时文件管理器实例
 */
export function getGlobalTempFileManager() {
  return globalTempFileManager;
}

/**
 * 在进程退出时清理临时文件
 */
function setupCleanupOnExit() {
  const cleanup = () => {
    globalTempFileManager.cleanup();
  };

  process.on('exit', cleanup);
  process.on('SIGINT', cleanup);
  process.on('SIGTERM', cleanup);
  process.on('uncaughtException', cleanup);
}

// 自动设置清理
setupCleanupOnExit();
