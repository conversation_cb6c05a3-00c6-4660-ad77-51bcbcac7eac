/**
 * JSON扫描器工具函数
 * @description 重构自原始文件中的JSON扫描器代码，对应第4356-4400行
 * @original 原始代码行 4356-4400
 */

/**
 * JSON扫描错误类型
 * @original aIA枚举
 */
export const JSONScanError = {
  None: 0,
  UnexpectedEndOfComment: 1,
  UnexpectedEndOfString: 2,
  UnexpectedEndOfNumber: 3,
  InvalidUnicode: 4,
  InvalidEscapeCharacter: 5,
  InvalidCharacter: 6
};

/**
 * JSON Token类型
 * @original sIA枚举
 */
export const JSONTokenType = {
  OpenBraceToken: 1,      // {
  CloseBraceToken: 2,     // }
  OpenBracketToken: 3,    // [
  CloseBracketToken: 4,   // ]
  CommaToken: 5,          // ,
  ColonToken: 6,          // :
  NullKeyword: 7,         // null
  TrueKeyword: 8,         // true
  FalseKeyword: 9,        // false
  StringLiteral: 10,      // "string"
  NumericLiteral: 11,     // 123
  LineCommentTrivia: 12,  // // comment
  BlockCommentTrivia: 13, // /* comment */
  LineBreakTrivia: 14,    // \n
  Trivia: 15,             // whitespace
  Unknown: 16,            // unknown
  EOF: 17                 // end of file
};

/**
 * JSON解析错误类型
 * @original rIA枚举
 */
export const JSONParseError = {
  InvalidSymbol: 1,
  InvalidNumberFormat: 2,
  PropertyNameExpected: 3,
  ValueExpected: 4,
  ColonExpected: 5,
  CommaExpected: 6,
  CloseBraceExpected: 7,
  CloseBracketExpected: 8,
  EndOfFileExpected: 9,
  InvalidCommentToken: 10,
  UnexpectedEndOfComment: 11,
  UnexpectedEndOfString: 12,
  UnexpectedEndOfNumber: 13,
  InvalidUnicode: 14,
  InvalidEscapeCharacter: 15,
  InvalidCharacter: 16
};

/**
 * JSON扫描器类
 * @description 提供JSON词法分析功能
 */
export class JSONScanner {
  constructor(text, ignoreTrivia = false) {
    this.text = text;
    this.length = text.length;
    this.position = 0;
    this.ignoreTrivia = ignoreTrivia;
    this.token = JSONTokenType.Unknown;
    this.tokenOffset = 0;
    this.tokenLength = 0;
    this.tokenError = JSONScanError.None;
    this.tokenValue = undefined;
  }

  /**
   * 扫描下一个token
   * @returns {number} token类型
   */
  scan() {
    this.tokenOffset = this.position;
    this.tokenError = JSONScanError.None;
    this.tokenValue = undefined;

    if (this.position >= this.length) {
      this.tokenLength = 0;
      return this.token = JSONTokenType.EOF;
    }

    const char = this.text.charCodeAt(this.position);

    // 跳过空白字符
    if (this.isWhitespace(char)) {
      return this.scanWhitespace();
    }

    // 扫描换行符
    if (this.isLineBreak(char)) {
      return this.scanLineBreak();
    }

    // 扫描具体的token
    switch (char) {
      case 123: // {
        this.position++;
        return this.token = JSONTokenType.OpenBraceToken;
      case 125: // }
        this.position++;
        return this.token = JSONTokenType.CloseBraceToken;
      case 91: // [
        this.position++;
        return this.token = JSONTokenType.OpenBracketToken;
      case 93: // ]
        this.position++;
        return this.token = JSONTokenType.CloseBracketToken;
      case 44: // ,
        this.position++;
        return this.token = JSONTokenType.CommaToken;
      case 58: // :
        this.position++;
        return this.token = JSONTokenType.ColonToken;
      case 34: // "
        return this.scanString();
      case 47: // /
        return this.scanComment();
      case 45: // -
      case 48: case 49: case 50: case 51: case 52: // 0-4
      case 53: case 54: case 55: case 56: case 57: // 5-9
        return this.scanNumber();
      case 116: // t
        return this.scanKeyword('true', JSONTokenType.TrueKeyword, true);
      case 102: // f
        return this.scanKeyword('false', JSONTokenType.FalseKeyword, false);
      case 110: // n
        return this.scanKeyword('null', JSONTokenType.NullKeyword, null);
      default:
        this.position++;
        this.tokenError = JSONScanError.InvalidCharacter;
        return this.token = JSONTokenType.Unknown;
    }
  }

  /**
   * 扫描空白字符
   * @returns {number} token类型
   * @private
   */
  scanWhitespace() {
    const start = this.position;
    
    while (this.position < this.length && this.isWhitespace(this.text.charCodeAt(this.position))) {
      this.position++;
    }
    
    this.tokenLength = this.position - start;
    
    if (this.ignoreTrivia) {
      return this.scan();
    }
    
    return this.token = JSONTokenType.Trivia;
  }

  /**
   * 扫描换行符
   * @returns {number} token类型
   * @private
   */
  scanLineBreak() {
    const char = this.text.charCodeAt(this.position);
    this.position++;
    
    if (char === 13 && this.position < this.length && this.text.charCodeAt(this.position) === 10) {
      this.position++; // \r\n
    }
    
    this.tokenLength = this.position - this.tokenOffset;
    
    if (this.ignoreTrivia) {
      return this.scan();
    }
    
    return this.token = JSONTokenType.LineBreakTrivia;
  }

  /**
   * 扫描字符串
   * @returns {number} token类型
   * @private
   */
  scanString() {
    this.position++; // 跳过开始的引号
    let value = '';
    
    while (this.position < this.length) {
      const char = this.text.charCodeAt(this.position);
      
      if (char === 34) { // 结束引号
        this.position++;
        this.tokenLength = this.position - this.tokenOffset;
        this.tokenValue = value;
        return this.token = JSONTokenType.StringLiteral;
      }
      
      if (char === 92) { // 反斜杠转义
        this.position++;
        if (this.position >= this.length) {
          this.tokenError = JSONScanError.UnexpectedEndOfString;
          break;
        }
        
        const escaped = this.text.charCodeAt(this.position);
        switch (escaped) {
          case 34: value += '"'; break;  // \"
          case 92: value += '\\'; break; // \\
          case 47: value += '/'; break;  // \/
          case 98: value += '\b'; break; // \b
          case 102: value += '\f'; break; // \f
          case 110: value += '\n'; break; // \n
          case 114: value += '\r'; break; // \r
          case 116: value += '\t'; break; // \t
          case 117: // \u
            if (this.position + 4 < this.length) {
              const hexCode = this.text.substr(this.position + 1, 4);
              if (/^[0-9a-fA-F]{4}$/.test(hexCode)) {
                value += String.fromCharCode(parseInt(hexCode, 16));
                this.position += 4;
              } else {
                this.tokenError = JSONScanError.InvalidUnicode;
              }
            } else {
              this.tokenError = JSONScanError.InvalidUnicode;
            }
            break;
          default:
            this.tokenError = JSONScanError.InvalidEscapeCharacter;
            break;
        }
      } else if (char < 32) {
        // 控制字符
        this.tokenError = JSONScanError.InvalidCharacter;
        break;
      } else {
        value += String.fromCharCode(char);
      }
      
      this.position++;
    }
    
    this.tokenLength = this.position - this.tokenOffset;
    this.tokenError = JSONScanError.UnexpectedEndOfString;
    return this.token = JSONTokenType.Unknown;
  }

  /**
   * 扫描注释
   * @returns {number} token类型
   * @private
   */
  scanComment() {
    this.position++; // 跳过第一个 /
    
    if (this.position >= this.length) {
      this.tokenError = JSONScanError.InvalidCharacter;
      return this.token = JSONTokenType.Unknown;
    }
    
    const nextChar = this.text.charCodeAt(this.position);
    
    if (nextChar === 47) { // 行注释 //
      this.position++;
      while (this.position < this.length && !this.isLineBreak(this.text.charCodeAt(this.position))) {
        this.position++;
      }
      this.tokenLength = this.position - this.tokenOffset;
      return this.token = JSONTokenType.LineCommentTrivia;
    } else if (nextChar === 42) { // 块注释 /*
      this.position++;
      while (this.position < this.length - 1) {
        if (this.text.charCodeAt(this.position) === 42 && this.text.charCodeAt(this.position + 1) === 47) {
          this.position += 2;
          this.tokenLength = this.position - this.tokenOffset;
          return this.token = JSONTokenType.BlockCommentTrivia;
        }
        this.position++;
      }
      this.tokenError = JSONScanError.UnexpectedEndOfComment;
      this.tokenLength = this.position - this.tokenOffset;
      return this.token = JSONTokenType.Unknown;
    } else {
      this.tokenError = JSONScanError.InvalidCharacter;
      return this.token = JSONTokenType.Unknown;
    }
  }

  /**
   * 扫描数字
   * @returns {number} token类型
   * @private
   */
  scanNumber() {
    const start = this.position;
    
    // 负号
    if (this.text.charCodeAt(this.position) === 45) {
      this.position++;
    }
    
    // 整数部分
    if (this.position >= this.length || !this.isDigit(this.text.charCodeAt(this.position))) {
      this.tokenError = JSONScanError.InvalidNumberFormat;
      return this.token = JSONTokenType.Unknown;
    }
    
    if (this.text.charCodeAt(this.position) === 48) {
      // 以0开头
      this.position++;
    } else {
      // 非0开头
      while (this.position < this.length && this.isDigit(this.text.charCodeAt(this.position))) {
        this.position++;
      }
    }
    
    // 小数部分
    if (this.position < this.length && this.text.charCodeAt(this.position) === 46) {
      this.position++;
      if (this.position >= this.length || !this.isDigit(this.text.charCodeAt(this.position))) {
        this.tokenError = JSONScanError.InvalidNumberFormat;
        return this.token = JSONTokenType.Unknown;
      }
      while (this.position < this.length && this.isDigit(this.text.charCodeAt(this.position))) {
        this.position++;
      }
    }
    
    // 指数部分
    if (this.position < this.length && (this.text.charCodeAt(this.position) === 101 || this.text.charCodeAt(this.position) === 69)) {
      this.position++;
      if (this.position < this.length && (this.text.charCodeAt(this.position) === 43 || this.text.charCodeAt(this.position) === 45)) {
        this.position++;
      }
      if (this.position >= this.length || !this.isDigit(this.text.charCodeAt(this.position))) {
        this.tokenError = JSONScanError.InvalidNumberFormat;
        return this.token = JSONTokenType.Unknown;
      }
      while (this.position < this.length && this.isDigit(this.text.charCodeAt(this.position))) {
        this.position++;
      }
    }
    
    this.tokenLength = this.position - this.tokenOffset;
    const numberText = this.text.substring(start, this.position);
    this.tokenValue = parseFloat(numberText);
    
    return this.token = JSONTokenType.NumericLiteral;
  }

  /**
   * 扫描关键字
   * @param {string} keyword - 关键字
   * @param {number} tokenType - token类型
   * @param {*} value - 值
   * @returns {number} token类型
   * @private
   */
  scanKeyword(keyword, tokenType, value) {
    if (this.text.substr(this.position, keyword.length) === keyword) {
      this.position += keyword.length;
      this.tokenLength = keyword.length;
      this.tokenValue = value;
      return this.token = tokenType;
    } else {
      this.position++;
      this.tokenError = JSONScanError.InvalidCharacter;
      return this.token = JSONTokenType.Unknown;
    }
  }

  /**
   * 检查是否为空白字符
   * @param {number} char - 字符编码
   * @returns {boolean} 是否为空白字符
   * @private
   */
  isWhitespace(char) {
    return char === 32 || char === 9; // 空格或制表符
  }

  /**
   * 检查是否为换行符
   * @param {number} char - 字符编码
   * @returns {boolean} 是否为换行符
   * @private
   */
  isLineBreak(char) {
    return char === 10 || char === 13; // \n 或 \r
  }

  /**
   * 检查是否为数字
   * @param {number} char - 字符编码
   * @returns {boolean} 是否为数字
   * @private
   */
  isDigit(char) {
    return char >= 48 && char <= 57; // 0-9
  }

  /**
   * 获取当前token类型
   * @returns {number} token类型
   */
  getToken() {
    return this.token;
  }

  /**
   * 获取token偏移量
   * @returns {number} 偏移量
   */
  getTokenOffset() {
    return this.tokenOffset;
  }

  /**
   * 获取token长度
   * @returns {number} 长度
   */
  getTokenLength() {
    return this.tokenLength;
  }

  /**
   * 获取token值
   * @returns {*} token值
   */
  getTokenValue() {
    return this.tokenValue;
  }

  /**
   * 获取token错误
   * @returns {number} 错误类型
   */
  getTokenError() {
    return this.tokenError;
  }

  /**
   * 获取当前位置
   * @returns {number} 位置
   */
  getPosition() {
    return this.position;
  }

  /**
   * 设置位置
   * @param {number} position - 新位置
   */
  setPosition(position) {
    this.position = Math.max(0, Math.min(position, this.length));
  }
}

/**
 * 创建JSON扫描器
 * @param {string} text - 要扫描的文本
 * @param {boolean} ignoreTrivia - 是否忽略空白字符
 * @returns {JSONScanner} JSON扫描器实例
 * @original j91函数
 */
export function createJSONScanner(text, ignoreTrivia = false) {
  return new JSONScanner(text, ignoreTrivia);
}

/**
 * JSON扫描器工具函数
 */
export const JSONScannerUtils = {
  /**
   * 扫描JSON文本获取所有tokens
   * @param {string} text - JSON文本
   * @param {boolean} ignoreTrivia - 是否忽略空白字符
   * @returns {Array} token数组
   */
  scanAll(text, ignoreTrivia = false) {
    const scanner = createJSONScanner(text, ignoreTrivia);
    const tokens = [];
    
    let token = scanner.scan();
    while (token !== JSONTokenType.EOF) {
      tokens.push({
        type: token,
        offset: scanner.getTokenOffset(),
        length: scanner.getTokenLength(),
        value: scanner.getTokenValue(),
        error: scanner.getTokenError()
      });
      token = scanner.scan();
    }
    
    return tokens;
  },

  /**
   * 验证JSON语法
   * @param {string} text - JSON文本
   * @returns {Array} 错误数组
   */
  validateSyntax(text) {
    const errors = [];
    const scanner = createJSONScanner(text, true);
    
    let token = scanner.scan();
    while (token !== JSONTokenType.EOF) {
      if (scanner.getTokenError() !== JSONScanError.None) {
        errors.push({
          offset: scanner.getTokenOffset(),
          length: scanner.getTokenLength(),
          error: scanner.getTokenError(),
          message: this.getErrorMessage(scanner.getTokenError())
        });
      }
      token = scanner.scan();
    }
    
    return errors;
  },

  /**
   * 获取错误消息
   * @param {number} errorType - 错误类型
   * @returns {string} 错误消息
   */
  getErrorMessage(errorType) {
    switch (errorType) {
      case JSONScanError.UnexpectedEndOfComment:
        return 'Unexpected end of comment';
      case JSONScanError.UnexpectedEndOfString:
        return 'Unexpected end of string';
      case JSONScanError.UnexpectedEndOfNumber:
        return 'Unexpected end of number';
      case JSONScanError.InvalidUnicode:
        return 'Invalid unicode sequence';
      case JSONScanError.InvalidEscapeCharacter:
        return 'Invalid escape character';
      case JSONScanError.InvalidCharacter:
        return 'Invalid character';
      default:
        return 'Unknown error';
    }
  }
};
