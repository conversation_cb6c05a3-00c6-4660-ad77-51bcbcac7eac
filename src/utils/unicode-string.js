/**
 * Unicode字符串处理工具函数
 * @description 重构自原始文件中的Unicode字符串处理函数，对应第1575-1634行
 * @original 原始代码行 1575-1634
 */

import { castSlice } from './array-slice.js';

/**
 * Unicode字符范围常量
 * @original 第1575-1582行的Unicode范围定义
 */
const UNICODE_RANGES = {
  // 高代理对和低代理对范围
  surrogatePair: "\\ud800-\\udfff",
  // 组合字符范围
  combiningMarks: "\\u0300-\\u036f",
  // 变体选择器范围
  variationSelectors: "\\ufe20-\\ufe2f",
  // 组合双重标记范围
  combiningDoubleMarks: "\\u20d0-\\u20ff",
  // 表情符号变体选择器
  emojiVariationSelectors: "\\ufe0e\\ufe0f",
  // 零宽连接符
  zeroWidthJoiner: "\\u200d"
};

/**
 * 组合所有组合字符范围
 */
const COMBINING_MARKS = UNICODE_RANGES.combiningMarks + 
                       UNICODE_RANGES.variationSelectors + 
                       UNICODE_RANGES.combiningDoubleMarks;

/**
 * 复杂Unicode字符检测正则表达式
 * @original var RnB = RegExp("[" + MnB + UnB + NnB + LnB + "]");
 */
const COMPLEX_UNICODE_REGEX = new RegExp("[" + 
  UNICODE_RANGES.zeroWidthJoiner + 
  UNICODE_RANGES.surrogatePair + 
  COMBINING_MARKS + 
  UNICODE_RANGES.emojiVariationSelectors + 
"]");

/**
 * 检查字符串是否包含复杂Unicode字符
 * @param {string} string - 要检查的字符串
 * @returns {boolean} 是否包含复杂Unicode字符
 * @original function OnB(A) { return RnB.test(A); }
 */
export function hasComplexUnicode(string) {
  return COMPLEX_UNICODE_REGEX.test(string);
}

/**
 * 简单字符串分割函数
 * @param {string} string - 要分割的字符串
 * @returns {Array} 字符数组
 * @original function TnB(A) { return A.split(""); }
 */
function asciiToArray(string) {
  return string.split("");
}

/**
 * Unicode字符串分割的复杂正则表达式
 * @original 第1591-1610行的复杂正则表达式构建
 */
const UNICODE_CHAR_RANGES = {
  surrogatePair: "\\ud800-\\udfff",
  combiningMarks: "\\u0300-\\u036f",
  variationSelectors: "\\ufe20-\\ufe2f",
  combiningDoubleMarks: "\\u20d0-\\u20ff",
  emojiVariationSelectors: "\\ufe0e\\ufe0f"
};

// 构建复杂的Unicode字符匹配正则表达式
const SURROGATE_PAIR = "[" + UNICODE_CHAR_RANGES.surrogatePair + "]";
const COMBINING_MARK = "[" + 
  UNICODE_CHAR_RANGES.combiningMarks + 
  UNICODE_CHAR_RANGES.variationSelectors + 
  UNICODE_CHAR_RANGES.combiningDoubleMarks + 
"]";

const SKIN_TONE_MODIFIER = "\\ud83c[\\udffb-\\udfff]";
const OPTIONAL_MODIFIER = "(?:" + COMBINING_MARK + "|" + SKIN_TONE_MODIFIER + ")";
const NON_SURROGATE = "[^" + UNICODE_CHAR_RANGES.surrogatePair + "]";
const REGIONAL_INDICATOR = "(?:\\ud83c[\\udde6-\\uddff]){2}";
const SURROGATE_PAIR_RANGE = "[\\ud800-\\udbff][\\udc00-\\udfff]";
const ZERO_WIDTH_JOINER = "\\u200d";

const OPTIONAL_MODIFIER_MARK = OPTIONAL_MODIFIER + "?";
const OPTIONAL_VARIATION_SELECTOR = "[" + UNICODE_CHAR_RANGES.emojiVariationSelectors + "]?";
const OPTIONAL_JOIN_SEQUENCE = "(?:" + ZERO_WIDTH_JOINER + 
  "(?:" + [NON_SURROGATE, REGIONAL_INDICATOR, SURROGATE_PAIR_RANGE].join("|") + ")" + 
  OPTIONAL_VARIATION_SELECTOR + OPTIONAL_MODIFIER_MARK + ")*";

const EMOJI_SEQUENCE = OPTIONAL_VARIATION_SELECTOR + OPTIONAL_MODIFIER_MARK + OPTIONAL_JOIN_SEQUENCE;
const UNICODE_CHAR = "(?:" + [
  NON_SURROGATE + COMBINING_MARK + "?", 
  COMBINING_MARK, 
  REGIONAL_INDICATOR, 
  SURROGATE_PAIR_RANGE, 
  SURROGATE_PAIR
].join("|") + ")";

/**
 * Unicode字符分割正则表达式
 * @original var gnB = RegExp(ku1 + "(?=" + ku1 + ")|" + hnB + fnB, "g");
 */
const UNICODE_SPLIT_REGEX = new RegExp(
  SKIN_TONE_MODIFIER + "(?=" + SKIN_TONE_MODIFIER + ")|" + 
  UNICODE_CHAR + EMOJI_SEQUENCE, 
  "g"
);

/**
 * 将Unicode字符串分割为字符数组
 * @param {string} string - 要分割的字符串
 * @returns {Array} 字符数组
 * @original function unB(A) { return A.match(gnB) || []; }
 */
function unicodeToArray(string) {
  return string.match(UNICODE_SPLIT_REGEX) || [];
}

/**
 * 将字符串转换为字符数组（支持Unicode）
 * @param {string} string - 要转换的字符串
 * @returns {Array} 字符数组
 * @original function mnB(A) { return SY1(A) ? lP0(A) : fP0(A); }
 */
export function stringToArray(string) {
  return hasComplexUnicode(string) ? unicodeToArray(string) : asciiToArray(string);
}

/**
 * 创建字符串大小写转换函数
 * @param {string} methodName - 要调用的方法名（如'toUpperCase'）
 * @returns {Function} 转换函数
 * @original function dnB(A) { return function (B) { B = hc(B); var Q = SY1(B) ? pP0(B) : void 0, D = Q ? Q[0] : B.charAt(0), Z = Q ? bP0(Q, 1).join("") : B.slice(1); return D[A]() + Z; }; }
 */
function createCaseFirst(methodName) {
  return function(string) {
    string = String(string);
    
    const strSymbols = hasComplexUnicode(string) ? stringToArray(string) : undefined;
    const chr = strSymbols ? strSymbols[0] : string.charAt(0);
    const trailing = strSymbols ? castSlice(strSymbols, 1).join("") : string.slice(1);
    
    return chr[methodName]() + trailing;
  };
}

/**
 * 将字符串首字母转换为大写
 * @original var cnB = iP0("toUpperCase"), nP0 = cnB;
 */
const upperFirst = createCaseFirst("toUpperCase");

/**
 * 将字符串首字母大写，其余小写
 * @param {string} string - 要处理的字符串
 * @returns {string} 首字母大写的字符串
 * @original function lnB(A) { return nP0(hc(A).toLowerCase()); }
 */
export function capitalize(string) {
  return upperFirst(String(string).toLowerCase());
}

/**
 * 将字符串首字母转换为小写
 */
export const lowerFirst = createCaseFirst("toLowerCase");

/**
 * 获取字符串的实际长度（支持Unicode）
 * @param {string} string - 要测量的字符串
 * @returns {number} 字符串长度
 */
export function size(string) {
  return string == null ? 0 : 
    hasComplexUnicode(string) ? unicodeToArray(string).length : string.length;
}

/**
 * 截取字符串（支持Unicode）
 * @param {string} string - 要截取的字符串
 * @param {number} start - 开始位置
 * @param {number} end - 结束位置
 * @returns {string} 截取后的字符串
 */
export function substr(string, start = 0, end) {
  if (string == null) {
    return "";
  }
  
  if (!hasComplexUnicode(string)) {
    return string.slice(start, end);
  }
  
  const strSymbols = stringToArray(string);
  const sliced = castSlice(strSymbols, start, end);
  return sliced.join("");
}

/**
 * 反转字符串（支持Unicode）
 * @param {string} string - 要反转的字符串
 * @returns {string} 反转后的字符串
 */
export function reverse(string) {
  if (string == null) {
    return "";
  }
  
  if (!hasComplexUnicode(string)) {
    return string.split("").reverse().join("");
  }
  
  return stringToArray(string).reverse().join("");
}

/**
 * 检查字符串是否以指定字符串开头（支持Unicode）
 * @param {string} string - 要检查的字符串
 * @param {string} target - 目标字符串
 * @param {number} position - 开始位置
 * @returns {boolean} 是否以目标字符串开头
 */
export function startsWith(string, target, position = 0) {
  if (string == null || target == null) {
    return false;
  }
  
  string = String(string);
  target = String(target);
  
  if (!hasComplexUnicode(string) && !hasComplexUnicode(target)) {
    return string.startsWith(target, position);
  }
  
  const strSymbols = stringToArray(string);
  const targetSymbols = stringToArray(target);
  
  if (position < 0 || position >= strSymbols.length) {
    return false;
  }
  
  for (let i = 0; i < targetSymbols.length; i++) {
    if (strSymbols[position + i] !== targetSymbols[i]) {
      return false;
    }
  }
  
  return true;
}

/**
 * 检查字符串是否以指定字符串结尾（支持Unicode）
 * @param {string} string - 要检查的字符串
 * @param {string} target - 目标字符串
 * @param {number} position - 结束位置
 * @returns {boolean} 是否以目标字符串结尾
 */
export function endsWith(string, target, position) {
  if (string == null || target == null) {
    return false;
  }
  
  string = String(string);
  target = String(target);
  
  if (!hasComplexUnicode(string) && !hasComplexUnicode(target)) {
    return string.endsWith(target, position);
  }
  
  const strSymbols = stringToArray(string);
  const targetSymbols = stringToArray(target);
  
  const length = strSymbols.length;
  position = position === undefined ? length : Math.min(Math.max(position, 0), length);
  
  const start = position - targetSymbols.length;
  if (start < 0) {
    return false;
  }
  
  for (let i = 0; i < targetSymbols.length; i++) {
    if (strSymbols[start + i] !== targetSymbols[i]) {
      return false;
    }
  }
  
  return true;
}
