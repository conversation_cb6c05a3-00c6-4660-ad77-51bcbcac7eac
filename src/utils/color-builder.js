/**
 * 颜色样式构建器
 * @description 重构自原始文件中的颜色样式构建代码，对应第2600-2660行
 * @original 原始代码行 2600-2660
 */

import { colorStyles, replaceAll } from './terminal-colors.js';
import { replaceAll as stringReplaceAll } from './string-replace.js';

/**
 * 颜色级别符号
 * @original Oi1, Cp, jB1符号
 */
const LEVEL_SYMBOL = Symbol('level');
const STYLE_SYMBOL = Symbol('style');
const VISIBLE_SYMBOL = Symbol('visible');

/**
 * 颜色样式属性定义
 * @description 为每种颜色创建getter属性
 * @original Kp对象的构建
 */
const colorProperties = {};

// 为每种颜色创建属性
const colorNames = [
  'black', 'red', 'green', 'yellow', 'blue', 'magenta', 'cyan', 'white',
  'blackBright', 'gray', 'grey', 'redBright', 'greenBright', 'yellowBright',
  'blueBright', 'magentaBright', 'cyanBright', 'whiteBright'
];

for (const colorName of colorNames) {
  // 前景色属性
  colorProperties[colorName] = {
    get() {
      const { level } = this;
      return function(...args) {
        const style = createStyle(colorName, colorLevels[level], "color", ...args);
        const closeCode = colorStyles.color.close;
        const parentStyle = this[STYLE_SYMBOL];
        
        return createColorFunction(this, style, closeCode, parentStyle);
      };
    }
  };

  // 背景色属性
  const bgColorName = "bg" + colorName[0].toUpperCase() + colorName.slice(1);
  colorProperties[bgColorName] = {
    get() {
      const { level } = this;
      return function(...args) {
        const style = createStyle(colorName, colorLevels[level], "bgColor", ...args);
        const closeCode = colorStyles.bgColor.close;
        const parentStyle = this[STYLE_SYMBOL];
        
        return createColorFunction(this, style, closeCode, parentStyle);
      };
    }
  };
}

/**
 * 颜色级别映射
 * @original fo0数组
 */
const colorLevels = [
  null,     // 级别0: 无颜色
  'ansi',   // 级别1: 基本ANSI颜色
  'ansi256', // 级别2: 256色
  'ansi16m'  // 级别3: 真彩色
];

/**
 * 创建样式对象
 * @param {string} colorName - 颜色名称
 * @param {string} levelType - 级别类型
 * @param {string} category - 颜色类别 (color/bgColor)
 * @param {...*} args - 额外参数
 * @returns {string} 样式代码
 * @original Ti1函数
 */
function createStyle(colorName, levelType, category, ...args) {
  if (!levelType) return '';
  
  const colorCategory = colorStyles[category];
  if (!colorCategory) return '';
  
  if (levelType === 'ansi') {
    // 基本ANSI颜色
    return colorCategory[colorName]?.open || '';
  } else if (levelType === 'ansi256' && args.length > 0) {
    // 256色
    return colorCategory.ansi256(args[0]);
  } else if (levelType === 'ansi16m' && args.length >= 3) {
    // 真彩色
    return colorCategory.ansi16m(args[0], args[1], args[2]);
  }
  
  return '';
}

/**
 * 创建样式信息对象
 * @param {string} open - 开启代码
 * @param {string} close - 关闭代码
 * @param {Object} parent - 父样式
 * @returns {Object} 样式信息对象
 * @original Pi1函数
 */
function createStyleInfo(open, close, parent) {
  let openAll, closeAll;
  
  if (parent === undefined) {
    openAll = open;
    closeAll = close;
  } else {
    openAll = parent.openAll + open;
    closeAll = close + parent.closeAll;
  }
  
  return {
    open,
    close,
    openAll,
    closeAll,
    parent
  };
}

/**
 * 创建颜色函数
 * @param {Object} context - 上下文对象
 * @param {Object} styleInfo - 样式信息
 * @param {Object} parentStyle - 父样式
 * @returns {Function} 颜色函数
 * @original uV1函数
 */
function createColorFunction(context, styleInfo, parentStyle) {
  const colorFunction = (...args) => {
    const text = args.length === 1 ? String(args[0]) : args.join(" ");
    return applyColorStyle(colorFunction, text);
  };
  
  // 设置原型和属性
  Object.setPrototypeOf(colorFunction, colorBuilder);
  colorFunction[LEVEL_SYMBOL] = context;
  colorFunction[STYLE_SYMBOL] = styleInfo;
  colorFunction[VISIBLE_SYMBOL] = parentStyle;
  
  return colorFunction;
}

/**
 * 应用颜色样式到文本
 * @param {Function} colorFunction - 颜色函数
 * @param {string} text - 要着色的文本
 * @returns {string} 着色后的文本
 * @original Gx9函数
 */
function applyColorStyle(colorFunction, text) {
  if (colorFunction.level <= 0 || !text) {
    return colorFunction[VISIBLE_SYMBOL] ? "" : text;
  }
  
  const styleInfo = colorFunction[STYLE_SYMBOL];
  if (styleInfo === undefined) return text;
  
  const { openAll, closeAll } = styleInfo;
  
  // 处理嵌套的ANSI转义序列
  if (text.includes("\x1B")) {
    let currentStyle = styleInfo;
    while (currentStyle !== undefined) {
      text = stringReplaceAll(text, currentStyle.close, currentStyle.open);
      currentStyle = currentStyle.parent;
    }
  }
  
  // 处理多行文本
  const newlineIndex = text.indexOf('\n');
  if (newlineIndex !== -1) {
    text = handleMultilineText(text, closeAll, openAll, newlineIndex);
  }
  
  return openAll + text + closeAll;
}

/**
 * 处理多行文本的颜色应用
 * @param {string} text - 文本
 * @param {string} closeAll - 关闭代码
 * @param {string} openAll - 开启代码
 * @param {number} newlineIndex - 换行符索引
 * @returns {string} 处理后的文本
 * @original xo0函数
 */
function handleMultilineText(text, closeAll, openAll, newlineIndex) {
  const lines = text.split('\n');
  return lines.map((line, index) => {
    if (index === 0) return line;
    return openAll + line;
  }).join('\n' + closeAll);
}

/**
 * 颜色构建器基础对象
 * @description 提供颜色样式的基础功能
 * @original Zx9对象
 */
const colorBuilder = Object.defineProperties(() => {}, {
  ...colorProperties,
  level: {
    enumerable: true,
    get() {
      return this[LEVEL_SYMBOL].level;
    },
    set(value) {
      this[LEVEL_SYMBOL].level = value;
    }
  }
});

/**
 * 颜色构建器类
 * @description 用于创建和管理颜色样式的类
 * @original yB1类
 */
export class ColorBuilder {
  constructor(level = 1) {
    this.level = level;
  }

  /**
   * 设置颜色级别
   * @param {number} level - 颜色级别 (0-3)
   */
  setLevel(level) {
    this.level = Math.max(0, Math.min(3, level));
  }

  /**
   * 获取颜色级别
   * @returns {number} 颜色级别
   */
  getLevel() {
    return this.level;
  }

  /**
   * 检查是否支持颜色
   * @returns {boolean} 是否支持颜色
   */
  supportsColor() {
    return this.level > 0;
  }

  /**
   * 创建颜色函数
   * @param {string} colorName - 颜色名称
   * @returns {Function} 颜色函数
   */
  createColorFunction(colorName) {
    const property = colorProperties[colorName];
    if (!property) {
      throw new Error(`Unknown color: ${colorName}`);
    }
    
    return property.get.call(this);
  }

  /**
   * 应用多个样式
   * @param {string} text - 文本
   * @param {...string} styleNames - 样式名称
   * @returns {string} 应用样式后的文本
   */
  applyStyles(text, ...styleNames) {
    let result = text;
    
    for (const styleName of styleNames) {
      const colorFunction = this.createColorFunction(styleName);
      result = colorFunction(result);
    }
    
    return result;
  }

  /**
   * 移除文本中的颜色代码
   * @param {string} text - 包含颜色代码的文本
   * @returns {string} 移除颜色代码后的文本
   */
  stripColors(text) {
    return text.replace(/\x1B\[[0-9;]*m/g, '');
  }
}

// 为ColorBuilder原型添加颜色属性
Object.defineProperties(ColorBuilder.prototype, colorProperties);

/**
 * 创建默认颜色构建器实例
 * @param {number} level - 颜色级别
 * @returns {ColorBuilder} 颜色构建器实例
 */
export function createColorBuilder(level = 1) {
  return new ColorBuilder(level);
}

/**
 * 全局颜色级别设置函数
 * @param {number} level - 颜色级别
 * @original go0函数
 */
let globalColorLevel = 1;

export function setGlobalColorLevel(level) {
  globalColorLevel = level;
}

/**
 * 获取全局颜色级别
 * @returns {number} 全局颜色级别
 */
export function getGlobalColorLevel() {
  return globalColorLevel;
}

/**
 * 空操作函数
 * @original uo0函数
 */
export function noop() {
  return;
}

/**
 * 获取默认颜色构建器
 * @returns {ColorBuilder} 默认颜色构建器
 * @original mo0函数
 */
export function getDefaultColorBuilder() {
  return createColorBuilder(globalColorLevel);
}

// 导出默认实例
export const defaultColorBuilder = createColorBuilder();

// 导出便捷的颜色函数
export const {
  black, red, green, yellow, blue, magenta, cyan, white,
  blackBright, gray, grey, redBright, greenBright, yellowBright,
  blueBright, magentaBright, cyanBright, whiteBright,
  bgBlack, bgRed, bgGreen, bgYellow, bgBlue, bgMagenta, bgCyan, bgWhite
} = defaultColorBuilder;
