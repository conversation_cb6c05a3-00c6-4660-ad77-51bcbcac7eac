/**
 * 集合操作工具函数
 * @description 重构自原始文件中的集合操作函数，对应第1750-1795行
 * @original 原始代码行 1750-1795
 */

/**
 * 检查集合中是否有元素满足条件
 * @param {Array|Object} collection - 要检查的集合
 * @param {Function} predicate - 判断函数
 * @returns {boolean} 是否有元素满足条件
 * @original function crB(A, B) { var Q = -1, D = A.length; while (++Q < D) if (B(A[Q])) return !0; return !1; }
 */
function arraySome(collection, predicate) {
  let index = -1;
  const length = collection.length;
  
  while (++index < length) {
    if (predicate(collection[index])) {
      return true;
    }
  }
  
  return false;
}

/**
 * 获取数组或对象的最后一个元素
 * @param {Array|string} array - 数组或字符串
 * @returns {*} 最后一个元素
 * @original function lrB(A) { var B = A == null ? 0 : A.length; return B ? A[B - 1] : void 0; }
 */
export function last(array) {
  const length = array == null ? 0 : array.length;
  return length ? array[length - 1] : undefined;
}

/**
 * 过滤集合中满足条件的元素
 * @param {Array|Object} collection - 要过滤的集合
 * @param {Function} predicate - 判断函数
 * @returns {Array} 过滤后的数组
 * @original function prB(A, B) { var Q = []; return AW1(A, function (D, Z, G) { if (B(D, Z, G)) Q.push(D); }), Q; }
 */
export function filter(collection, predicate) {
  const result = [];
  
  // @todo: 实现AW1函数的逻辑（可能是forEach的实现）
  forEach(collection, function(value, index, collection) {
    if (predicate(value, index, collection)) {
      result.push(value);
    }
  });
  
  return result;
}

/**
 * 根据路径数组获取对象的值
 * @param {Object} object - 源对象
 * @param {Array} paths - 路径数组
 * @returns {Array} 对应路径的值数组
 * @original function irB(A, B) { return Rc(B, function (Q) { return A[Q]; }); }
 */
export function at(object, paths) {
  return map(paths, function(path) {
    return object[path];
  });
}

/**
 * 获取对象的所有值
 * @param {Object} object - 源对象
 * @returns {Array} 值数组
 * @original function nrB(A) { return A == null ? [] : Yj0(A, vH(A)); }
 */
export function values(object) {
  return object == null ? [] : at(object, keys(object));
}

/**
 * 获取嵌套对象的父对象
 * @param {Object} object - 源对象
 * @param {Array} path - 路径数组
 * @returns {*} 父对象
 * @original function arB(A, B) { return B.length < 2 ? A : _j(A, PY1(B, 0, -1)); }
 */
export function parent(object, path) {
  return path.length < 2 ? object : get(object, slice(path, 0, -1));
}

/**
 * 检查两个值是否相等
 * @param {*} value - 第一个值
 * @param {*} other - 第二个值
 * @returns {boolean} 是否相等
 * @original function srB(A, B) { return ic(A, B); }
 */
export function isEqual(value, other) {
  // @todo: 实现ic函数的逻辑（深度相等比较）
  return baseIsEqual(value, other);
}

/**
 * 对集合中的每个元素执行函数并求和
 * @param {Array} array - 数组
 * @param {Function} iteratee - 迭代函数
 * @returns {number} 求和结果
 * @original function orB(A, B) { var Q, D = -1, Z = A.length; while (++D < Z) { var G = B(A[D]); if (G !== void 0) Q = Q === void 0 ? G : Q + G; } return Q; }
 */
export function sumBy(array, iteratee) {
  let result;
  let index = -1;
  const length = array.length;
  
  while (++index < length) {
    const current = iteratee(array[index]);
    if (current !== undefined) {
      result = result === undefined ? current : result + current;
    }
  }
  
  return result;
}

/**
 * 创建函数的否定版本
 * @param {Function} predicate - 要否定的函数
 * @returns {Function} 否定函数
 * @original function erB(A) { if (typeof A != "function") throw new TypeError(trB); return function () { ... }; }
 */
export function negate(predicate) {
  if (typeof predicate != "function") {
    throw new TypeError("Expected a function");
  }
  
  return function() {
    const args = arguments;
    switch (args.length) {
      case 0:
        return !predicate.call(this);
      case 1:
        return !predicate.call(this, args[0]);
      case 2:
        return !predicate.call(this, args[0], args[1]);
      case 3:
        return !predicate.call(this, args[0], args[1], args[2]);
    }
    return !predicate.apply(this, args);
  };
}

// 辅助函数 - 这些需要在其他模块中实现或从其他模块导入

/**
 * 遍历集合中的每个元素
 * @param {Array|Object} collection - 要遍历的集合
 * @param {Function} iteratee - 遍历函数
 * @returns {Array|Object} 原集合
 */
function forEach(collection, iteratee) {
  if (Array.isArray(collection)) {
    let index = -1;
    const length = collection.length;
    
    while (++index < length) {
      if (iteratee(collection[index], index, collection) === false) {
        break;
      }
    }
  } else if (collection != null) {
    const objKeys = keys(collection);
    let index = -1;
    const length = objKeys.length;
    
    while (++index < length) {
      const key = objKeys[index];
      if (iteratee(collection[key], key, collection) === false) {
        break;
      }
    }
  }
  
  return collection;
}

/**
 * 对集合中的每个元素执行映射函数
 * @param {Array} array - 要映射的数组
 * @param {Function} iteratee - 映射函数
 * @returns {Array} 映射后的数组
 */
function map(array, iteratee) {
  let index = -1;
  const length = array == null ? 0 : array.length;
  const result = Array(length);
  
  while (++index < length) {
    result[index] = iteratee(array[index], index, array);
  }
  
  return result;
}

/**
 * 获取对象的所有键
 * @param {Object} object - 源对象
 * @returns {Array} 键数组
 */
function keys(object) {
  return Object.keys(object || {});
}

/**
 * 数组切片函数
 * @param {Array} array - 要切片的数组
 * @param {number} start - 开始索引
 * @param {number} end - 结束索引
 * @returns {Array} 切片后的数组
 */
function slice(array, start, end) {
  return Array.prototype.slice.call(array, start, end);
}

/**
 * 根据路径获取对象的值
 * @param {Object} object - 源对象
 * @param {string|Array} path - 路径
 * @returns {*} 获取的值
 */
function get(object, path) {
  // @todo: 实现_j函数的逻辑
  if (object == null) {
    return undefined;
  }
  
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = object;
  
  for (const key of keys) {
    if (result == null) {
      return undefined;
    }
    result = result[key];
  }
  
  return result;
}

/**
 * 基础相等性比较
 * @param {*} value - 第一个值
 * @param {*} other - 第二个值
 * @returns {boolean} 是否相等
 */
function baseIsEqual(value, other) {
  if (value === other) {
    return true;
  }
  
  if (value == null || other == null) {
    return value === other;
  }
  
  // 简单的深度比较实现
  if (typeof value !== typeof other) {
    return false;
  }
  
  if (typeof value === 'object') {
    const valueKeys = Object.keys(value);
    const otherKeys = Object.keys(other);
    
    if (valueKeys.length !== otherKeys.length) {
      return false;
    }
    
    for (const key of valueKeys) {
      if (!otherKeys.includes(key) || !baseIsEqual(value[key], other[key])) {
        return false;
      }
    }
    
    return true;
  }
  
  return false;
}

/**
 * 检查集合是否为空
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
  if (value == null) {
    return true;
  }
  
  if (Array.isArray(value) || typeof value === 'string') {
    return value.length === 0;
  }
  
  if (typeof value === 'object') {
    return Object.keys(value).length === 0;
  }
  
  return false;
}

/**
 * 获取集合的大小
 * @param {*} collection - 集合
 * @returns {number} 大小
 */
export function size(collection) {
  if (collection == null) {
    return 0;
  }
  
  if (Array.isArray(collection) || typeof collection === 'string') {
    return collection.length;
  }
  
  if (typeof collection === 'object') {
    return Object.keys(collection).length;
  }
  
  return 0;
}

/**
 * 检查集合中是否包含指定值
 * @param {Array|Object|string} collection - 集合
 * @param {*} value - 要查找的值
 * @param {number} fromIndex - 开始查找的索引
 * @returns {boolean} 是否包含
 */
export function includes(collection, value, fromIndex = 0) {
  if (collection == null) {
    return false;
  }
  
  if (Array.isArray(collection) || typeof collection === 'string') {
    return collection.indexOf(value, fromIndex) > -1;
  }
  
  if (typeof collection === 'object') {
    const objValues = values(collection);
    return objValues.indexOf(value) > -1;
  }
  
  return false;
}
