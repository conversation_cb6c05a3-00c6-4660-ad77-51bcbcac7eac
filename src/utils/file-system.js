/**
 * 文件系统工具函数
 * @description 提供文件和目录操作的工具函数
 */

import { readFileSync as fsReadFileSync, existsSync, statSync, mkdirSync, writeFileSync as fsWriteFileSync } from 'fs';
import { resolve, join, dirname, basename, extname } from 'path';
import { homedir } from 'os';

/**
 * 检查文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {boolean} 文件是否存在
 * @original if (!KT0(s1)) process.stderr.write(A0.red(`Error: System prompt file not found: ${s1}`)), process.exit(1);
 */
export function fileExists(filePath) {
  try {
    return existsSync(filePath);
  } catch {
    return false;
  }
}

/**
 * 检查目录是否存在
 * @param {string} dirPath - 目录路径
 * @returns {boolean} 目录是否存在
 */
export function directoryExists(dirPath) {
  try {
    return existsSync(dirPath) && statSync(dirPath).isDirectory();
  } catch {
    return false;
  }
}

/**
 * 同步读取文件内容
 * @param {string} filePath - 文件路径
 * @param {string} encoding - 文件编码，默认为 'utf8'
 * @returns {string} 文件内容
 * @throws {Error} 文件读取失败时抛出错误
 * @original c = YR8(s1, "utf8");
 */
export function readFileSync(filePath, encoding = 'utf8') {
  try {
    return fsReadFileSync(filePath, encoding);
  } catch (error) {
    throw new Error(`Failed to read file ${filePath}: ${error.message}`);
  }
}

/**
 * 同步写入文件内容
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @param {string} encoding - 文件编码，默认为 'utf8'
 * @throws {Error} 文件写入失败时抛出错误
 */
export function writeFileSync(filePath, content, encoding = 'utf8') {
  try {
    // 确保目录存在
    const dir = dirname(filePath);
    if (!directoryExists(dir)) {
      mkdirSync(dir, { recursive: true });
    }
    
    fsWriteFileSync(filePath, content, encoding);
  } catch (error) {
    throw new Error(`Failed to write file ${filePath}: ${error.message}`);
  }
}

/**
 * 解析文件路径
 * @param {string} basePath - 基础路径
 * @param {string} relativePath - 相对路径
 * @returns {Object} 包含resolvedPath的对象
 * @original let { resolvedPath: I1 } = WV(x1(), O);
 */
export function resolveFilePath(basePath, relativePath) {
  if (!relativePath) {
    return { resolvedPath: basePath };
  }
  
  const resolvedPath = resolve(basePath, relativePath);
  return { resolvedPath };
}

/**
 * 获取用户主目录路径
 * @returns {string} 用户主目录路径
 */
export function getUserHomeDirectory() {
  return homedir();
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录路径
 */
export function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 连接路径
 * @param {...string} paths - 路径片段
 * @returns {string} 连接后的路径
 */
export function joinPath(...paths) {
  return join(...paths);
}

/**
 * 获取文件名（不含扩展名）
 * @param {string} filePath - 文件路径
 * @returns {string} 文件名
 */
export function getFileName(filePath) {
  const base = basename(filePath);
  const ext = extname(base);
  return base.slice(0, base.length - ext.length);
}

/**
 * 获取文件扩展名
 * @param {string} filePath - 文件路径
 * @returns {string} 文件扩展名（包含点号）
 */
export function getFileExtension(filePath) {
  return extname(filePath);
}

/**
 * 获取目录路径
 * @param {string} filePath - 文件路径
 * @returns {string} 目录路径
 */
export function getDirectoryPath(filePath) {
  return dirname(filePath);
}

/**
 * 获取文件基本名称（含扩展名）
 * @param {string} filePath - 文件路径
 * @returns {string} 文件基本名称
 */
export function getBaseName(filePath) {
  return basename(filePath);
}

/**
 * 创建目录（递归）
 * @param {string} dirPath - 目录路径
 * @throws {Error} 目录创建失败时抛出错误
 */
export function createDirectory(dirPath) {
  try {
    if (!directoryExists(dirPath)) {
      mkdirSync(dirPath, { recursive: true });
    }
  } catch (error) {
    throw new Error(`Failed to create directory ${dirPath}: ${error.message}`);
  }
}

/**
 * 获取文件统计信息
 * @param {string} filePath - 文件路径
 * @returns {Object|null} 文件统计信息，失败时返回null
 */
export function getFileStats(filePath) {
  try {
    return statSync(filePath);
  } catch {
    return null;
  }
}

/**
 * 检查路径是否为文件
 * @param {string} filePath - 文件路径
 * @returns {boolean} 是否为文件
 */
export function isFile(filePath) {
  try {
    return statSync(filePath).isFile();
  } catch {
    return false;
  }
}

/**
 * 检查路径是否为目录
 * @param {string} dirPath - 目录路径
 * @returns {boolean} 是否为目录
 */
export function isDirectory(dirPath) {
  try {
    return statSync(dirPath).isDirectory();
  } catch {
    return false;
  }
}

/**
 * 规范化路径
 * @param {string} filePath - 文件路径
 * @returns {string} 规范化后的路径
 */
export function normalizePath(filePath) {
  return resolve(filePath);
}

/**
 * 检查文件是否可读
 * @param {string} filePath - 文件路径
 * @returns {boolean} 文件是否可读
 */
export function isReadable(filePath) {
  try {
    fsReadFileSync(filePath, { encoding: 'utf8', flag: 'r' });
    return true;
  } catch {
    return false;
  }
}

/**
 * 检查文件是否可写
 * @param {string} filePath - 文件路径
 * @returns {boolean} 文件是否可写
 */
export function isWritable(filePath) {
  try {
    // 如果文件存在，检查是否可写
    if (fileExists(filePath)) {
      const stats = getFileStats(filePath);
      return stats && stats.mode & 0o200; // 检查写权限位
    }
    
    // 如果文件不存在，检查目录是否可写
    const dir = dirname(filePath);
    return directoryExists(dir);
  } catch {
    return false;
  }
}

/**
 * 获取临时目录路径
 * @returns {string} 临时目录路径
 */
export function getTempDirectory() {
  return process.env.TMPDIR || process.env.TMP || process.env.TEMP || '/tmp';
}

/**
 * 生成临时文件路径
 * @param {string} prefix - 文件名前缀
 * @param {string} suffix - 文件名后缀
 * @returns {string} 临时文件路径
 */
export function generateTempFilePath(prefix = 'temp', suffix = '') {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  const fileName = `${prefix}-${timestamp}-${random}${suffix}`;
  return joinPath(getTempDirectory(), fileName);
}

/**
 * 安全地删除文件
 * @param {string} filePath - 文件路径
 * @returns {boolean} 删除是否成功
 */
export function safeDeleteFile(filePath) {
  try {
    if (fileExists(filePath)) {
      const fs = require('fs');
      fs.unlinkSync(filePath);
      return true;
    }
    return false;
  } catch {
    return false;
  }
}
