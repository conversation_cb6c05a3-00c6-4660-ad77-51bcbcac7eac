/**
 * JSON解析器工具函数
 * @description 重构自原始文件中的JSON解析代码，对应第4076-4100行
 * @original 原始代码行 4076-4100
 */

/**
 * JSON解析选项
 * @original k91枚举
 */
export const JSONParseOptions = {
  DEFAULT: {
    allowTrailingComma: false
  }
};

/**
 * JSON节点类型
 */
export const JSONNodeType = {
  OBJECT: 'object',
  ARRAY: 'array',
  PROPERTY: 'property',
  STRING: 'string',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  NULL: 'null'
};

/**
 * JSON AST节点接口
 * @typedef {Object} JSONNode
 * @property {string} type - 节点类型
 * @property {number} offset - 在源文本中的偏移量
 * @property {number} length - 节点长度
 * @property {JSONNode[]} children - 子节点数组
 * @property {JSONNode} parent - 父节点
 * @property {string} [key] - 属性键（仅用于property类型）
 * @property {*} [value] - 节点值
 */

/**
 * JSON解析器类
 * @description 提供JSON解析为AST的功能
 */
export class JSONParser {
  constructor() {
    this.options = JSONParseOptions.DEFAULT;
  }

  /**
   * 解析JSON文本为AST
   * @param {string} text - JSON文本
   * @param {Array} errors - 错误数组（可选）
   * @param {Object} options - 解析选项
   * @returns {JSONNode} JSON AST根节点
   * @original Or1函数
   */
  parseTree(text, errors = [], options = JSONParseOptions.DEFAULT) {
    const rootNode = {
      type: JSONNodeType.ARRAY,
      offset: -1,
      length: -1,
      children: [],
      parent: undefined
    };

    let currentNode = rootNode;

    // 结束当前属性节点
    const endProperty = (offset) => {
      if (currentNode.type === JSONNodeType.PROPERTY) {
        currentNode.length = offset - currentNode.offset;
        currentNode = currentNode.parent;
      }
    };

    // 添加子节点
    const addChild = (node) => {
      currentNode.children.push(node);
      return node;
    };

    // 使用JSON访问器遍历
    this.visit(text, {
      onObjectBegin: (offset) => {
        currentNode = addChild({
          type: JSONNodeType.OBJECT,
          offset: offset,
          length: -1,
          children: [],
          parent: currentNode
        });
      },

      onObjectProperty: (property, offset, length) => {
        currentNode = addChild({
          type: JSONNodeType.PROPERTY,
          offset: offset,
          length: -1,
          children: [],
          parent: currentNode,
          key: property
        });
      },

      onObjectEnd: (offset, length) => {
        endProperty(offset + length);
        currentNode.length = offset + length - currentNode.offset;
        currentNode = currentNode.parent;
      },

      onArrayBegin: (offset) => {
        currentNode = addChild({
          type: JSONNodeType.ARRAY,
          offset: offset,
          length: -1,
          children: [],
          parent: currentNode
        });
      },

      onArrayEnd: (offset, length) => {
        currentNode.length = offset + length - currentNode.offset;
        currentNode = currentNode.parent;
      },

      onLiteralValue: (value, offset, length) => {
        endProperty(offset + length);
        
        let nodeType;
        if (value === null) {
          nodeType = JSONNodeType.NULL;
        } else if (typeof value === 'boolean') {
          nodeType = JSONNodeType.BOOLEAN;
        } else if (typeof value === 'number') {
          nodeType = JSONNodeType.NUMBER;
        } else {
          nodeType = JSONNodeType.STRING;
        }

        addChild({
          type: nodeType,
          offset: offset,
          length: length,
          children: [],
          parent: currentNode,
          value: value
        });
      },

      onSeparator: (character, offset, length) => {
        endProperty(offset + length);
      },

      onComment: (offset, length) => {
        // 注释处理（如果需要）
      },

      onError: (error, offset, length) => {
        errors.push({
          error: error,
          offset: offset,
          length: length
        });
      }
    }, options);

    return rootNode.children[0] || rootNode;
  }

  /**
   * 访问JSON文本
   * @param {string} text - JSON文本
   * @param {Object} visitor - 访问器对象
   * @param {Object} options - 选项
   * @original Tr1函数
   */
  visit(text, visitor, options = JSONParseOptions.DEFAULT) {
    const scanner = this.createScanner(text, false);
    
    const visitValue = () => {
      switch (scanner.getToken()) {
        case 1: // 左大括号
          return visitObject();
        case 3: // 左中括号
          return visitArray();
        case 10: // 字符串
        case 11: // 数字
        case 7:  // true
        case 8:  // false
        case 9:  // null
          return visitLiteral();
        default:
          return visitor.onError && visitor.onError(
            'Unexpected token',
            scanner.getTokenOffset(),
            scanner.getTokenLength()
          );
      }
    };

    const visitObject = () => {
      const offset = scanner.getTokenOffset();
      visitor.onObjectBegin && visitor.onObjectBegin(offset);
      
      scanner.scan(); // 跳过 {
      
      let needsComma = false;
      while (scanner.getToken() !== 2 && scanner.getToken() !== 17) { // 不是 } 或 EOF
        if (needsComma) {
          if (scanner.getToken() === 5) { // 逗号
            visitor.onSeparator && visitor.onSeparator(',', scanner.getTokenOffset(), scanner.getTokenLength());
            scanner.scan();
          } else {
            visitor.onError && visitor.onError(
              'Expected comma',
              scanner.getTokenOffset(),
              scanner.getTokenLength()
            );
          }
        }

        if (scanner.getToken() === 10) { // 字符串键
          const keyOffset = scanner.getTokenOffset();
          const keyLength = scanner.getTokenLength();
          const key = scanner.getTokenValue();
          
          visitor.onObjectProperty && visitor.onObjectProperty(key, keyOffset, keyLength);
          scanner.scan();
          
          if (scanner.getToken() === 6) { // 冒号
            visitor.onSeparator && visitor.onSeparator(':', scanner.getTokenOffset(), scanner.getTokenLength());
            scanner.scan();
            visitValue();
          } else {
            visitor.onError && visitor.onError(
              'Expected colon',
              scanner.getTokenOffset(),
              scanner.getTokenLength()
            );
          }
        } else {
          visitor.onError && visitor.onError(
            'Expected property name',
            scanner.getTokenOffset(),
            scanner.getTokenLength()
          );
        }
        
        needsComma = true;
      }

      if (scanner.getToken() === 2) { // 右大括号
        const endOffset = scanner.getTokenOffset();
        const endLength = scanner.getTokenLength();
        visitor.onObjectEnd && visitor.onObjectEnd(endOffset, endLength);
        scanner.scan();
      } else {
        visitor.onError && visitor.onError(
          'Expected closing brace',
          scanner.getTokenOffset(),
          scanner.getTokenLength()
        );
      }
    };

    const visitArray = () => {
      const offset = scanner.getTokenOffset();
      visitor.onArrayBegin && visitor.onArrayBegin(offset);
      
      scanner.scan(); // 跳过 [
      
      let needsComma = false;
      while (scanner.getToken() !== 4 && scanner.getToken() !== 17) { // 不是 ] 或 EOF
        if (needsComma) {
          if (scanner.getToken() === 5) { // 逗号
            visitor.onSeparator && visitor.onSeparator(',', scanner.getTokenOffset(), scanner.getTokenLength());
            scanner.scan();
          } else {
            visitor.onError && visitor.onError(
              'Expected comma',
              scanner.getTokenOffset(),
              scanner.getTokenLength()
            );
          }
        }
        
        visitValue();
        needsComma = true;
      }

      if (scanner.getToken() === 4) { // 右中括号
        const endOffset = scanner.getTokenOffset();
        const endLength = scanner.getTokenLength();
        visitor.onArrayEnd && visitor.onArrayEnd(endOffset, endLength);
        scanner.scan();
      } else {
        visitor.onError && visitor.onError(
          'Expected closing bracket',
          scanner.getTokenOffset(),
          scanner.getTokenLength()
        );
      }
    };

    const visitLiteral = () => {
      const offset = scanner.getTokenOffset();
      const length = scanner.getTokenLength();
      const value = scanner.getTokenValue();
      
      visitor.onLiteralValue && visitor.onLiteralValue(value, offset, length);
      scanner.scan();
    };

    scanner.scan();
    visitValue();
  }

  /**
   * 创建JSON扫描器
   * @param {string} text - 要扫描的文本
   * @param {boolean} ignoreTrivia - 是否忽略空白字符
   * @returns {Object} JSON扫描器
   */
  createScanner(text, ignoreTrivia = false) {
    // @todo: 实现完整的JSON扫描器
    // 这里返回一个简化的扫描器实现
    let offset = 0;
    let token = 0;
    let tokenOffset = 0;
    let tokenLength = 0;
    let tokenValue = null;

    const scanString = () => {
      let value = '';
      offset++; // 跳过开始的引号
      
      while (offset < text.length && text.charAt(offset) !== '"') {
        if (text.charAt(offset) === '\\') {
          offset++; // 跳过转义字符
          if (offset < text.length) {
            const escaped = text.charAt(offset);
            switch (escaped) {
              case 'n': value += '\n'; break;
              case 'r': value += '\r'; break;
              case 't': value += '\t'; break;
              case '\\': value += '\\'; break;
              case '"': value += '"'; break;
              default: value += escaped; break;
            }
          }
        } else {
          value += text.charAt(offset);
        }
        offset++;
      }
      
      if (offset < text.length) {
        offset++; // 跳过结束的引号
      }
      
      return value;
    };

    const scanNumber = () => {
      const start = offset;
      
      if (text.charAt(offset) === '-') {
        offset++;
      }
      
      while (offset < text.length && /\d/.test(text.charAt(offset))) {
        offset++;
      }
      
      if (offset < text.length && text.charAt(offset) === '.') {
        offset++;
        while (offset < text.length && /\d/.test(text.charAt(offset))) {
          offset++;
        }
      }
      
      if (offset < text.length && /[eE]/.test(text.charAt(offset))) {
        offset++;
        if (offset < text.length && /[+-]/.test(text.charAt(offset))) {
          offset++;
        }
        while (offset < text.length && /\d/.test(text.charAt(offset))) {
          offset++;
        }
      }
      
      return parseFloat(text.substring(start, offset));
    };

    return {
      scan() {
        // 跳过空白字符
        while (offset < text.length && /\s/.test(text.charAt(offset))) {
          offset++;
        }
        
        tokenOffset = offset;
        
        if (offset >= text.length) {
          token = 17; // EOF
          tokenLength = 0;
          return token;
        }
        
        const char = text.charAt(offset);
        tokenLength = 1;
        
        switch (char) {
          case '{':
            token = 1;
            offset++;
            break;
          case '}':
            token = 2;
            offset++;
            break;
          case '[':
            token = 3;
            offset++;
            break;
          case ']':
            token = 4;
            offset++;
            break;
          case ',':
            token = 5;
            offset++;
            break;
          case ':':
            token = 6;
            offset++;
            break;
          case '"':
            token = 10;
            tokenValue = scanString();
            tokenLength = offset - tokenOffset;
            break;
          case 't':
            if (text.substr(offset, 4) === 'true') {
              token = 7;
              tokenValue = true;
              tokenLength = 4;
              offset += 4;
            }
            break;
          case 'f':
            if (text.substr(offset, 5) === 'false') {
              token = 8;
              tokenValue = false;
              tokenLength = 5;
              offset += 5;
            }
            break;
          case 'n':
            if (text.substr(offset, 4) === 'null') {
              token = 9;
              tokenValue = null;
              tokenLength = 4;
              offset += 4;
            }
            break;
          default:
            if (/[-\d]/.test(char)) {
              token = 11;
              tokenValue = scanNumber();
              tokenLength = offset - tokenOffset;
            } else {
              token = 16; // 错误
              offset++;
            }
            break;
        }
        
        return token;
      },
      
      getToken() {
        return token;
      },
      
      getTokenOffset() {
        return tokenOffset;
      },
      
      getTokenLength() {
        return tokenLength;
      },
      
      getTokenValue() {
        return tokenValue;
      }
    };
  }
}

/**
 * JSON解析工具函数
 */
export const JSONParserUtils = {
  /**
   * 解析JSON字符串为AST
   * @param {string} jsonString - JSON字符串
   * @param {Array} errors - 错误数组（可选）
   * @returns {JSONNode} JSON AST
   */
  parseToAST(jsonString, errors = []) {
    const parser = new JSONParser();
    return parser.parseTree(jsonString, errors);
  },

  /**
   * 查找JSON路径对应的节点
   * @param {JSONNode} root - 根节点
   * @param {string[]} path - JSON路径数组
   * @returns {JSONNode|null} 找到的节点或null
   */
  findNodeByPath(root, path) {
    let current = root;
    
    for (const segment of path) {
      if (!current || !current.children) {
        return null;
      }
      
      if (current.type === JSONNodeType.OBJECT) {
        current = current.children.find(child => 
          child.type === JSONNodeType.PROPERTY && child.key === segment
        );
        if (current && current.children.length > 0) {
          current = current.children[0]; // 获取属性值
        }
      } else if (current.type === JSONNodeType.ARRAY) {
        const index = parseInt(segment, 10);
        if (isNaN(index) || index < 0 || index >= current.children.length) {
          return null;
        }
        current = current.children[index];
      } else {
        return null;
      }
    }
    
    return current;
  },

  /**
   * 获取节点的JSON路径
   * @param {JSONNode} node - 节点
   * @returns {string[]} JSON路径数组
   */
  getNodePath(node) {
    const path = [];
    let current = node;
    
    while (current && current.parent) {
      if (current.parent.type === JSONNodeType.OBJECT) {
        // 找到属性节点
        const propertyNode = current.parent.children.find(child =>
          child.type === JSONNodeType.PROPERTY && child.children.includes(current)
        );
        if (propertyNode) {
          path.unshift(propertyNode.key);
        }
      } else if (current.parent.type === JSONNodeType.ARRAY) {
        const index = current.parent.children.indexOf(current);
        path.unshift(index.toString());
      }
      
      current = current.parent;
    }
    
    return path;
  },

  /**
   * 获取节点的文本内容
   * @param {JSONNode} node - 节点
   * @param {string} text - 原始文本
   * @returns {string} 节点的文本内容
   */
  getNodeText(node, text) {
    if (node.offset >= 0 && node.length >= 0) {
      return text.substring(node.offset, node.offset + node.length);
    }
    return '';
  }
};

/**
 * 根据路径查找JSON节点
 * @param {JSONNode} root - 根节点
 * @param {Array} path - 路径数组
 * @returns {JSONNode|undefined} 找到的节点或undefined
 * @original yK1函数
 */
export function findNodeByPath(root, path) {
  if (!root) return undefined;

  let current = root;

  for (const segment of path) {
    if (typeof segment === "string") {
      // 对象属性查找
      if (current.type !== JSONNodeType.OBJECT || !Array.isArray(current.children)) {
        return undefined;
      }

      let found = false;
      for (const child of current.children) {
        if (Array.isArray(child.children) &&
            child.children[0] &&
            child.children[0].value === segment &&
            child.children.length === 2) {
          current = child.children[1];
          found = true;
          break;
        }
      }

      if (!found) return undefined;
    } else {
      // 数组索引查找
      const index = segment;
      if (current.type !== JSONNodeType.ARRAY ||
          index < 0 ||
          !Array.isArray(current.children) ||
          index >= current.children.length) {
        return undefined;
      }

      current = current.children[index];
    }
  }

  return current;
}

/**
 * 获取值的JSON类型
 * @param {*} value - 值
 * @returns {string} JSON类型名称
 * @original y5Q函数
 */
export function getJSONType(value) {
  switch (typeof value) {
    case "boolean":
      return JSONNodeType.BOOLEAN;
    case "number":
      return JSONNodeType.NUMBER;
    case "string":
      return JSONNodeType.STRING;
    case "object":
      if (!value) return JSONNodeType.NULL;
      if (Array.isArray(value)) return JSONNodeType.ARRAY;
      return JSONNodeType.OBJECT;
    default:
      return JSONNodeType.NULL;
  }
}

/**
 * JSON路径查找和编辑工具
 * @param {string} text - JSON文本
 * @param {Array} path - 路径数组
 * @param {Object} options - 选项
 * @param {Object} formattingOptions - 格式化选项
 * @returns {Object} 查找和编辑结果
 * @original nIA函数
 */
export function findAndEditJSON(text, path, options, formattingOptions) {
  const pathCopy = path.slice();
  const root = jsonParser.parseTree(text, []);
  let node = undefined;
  let parent = undefined;

  // 查找目标节点和父节点
  if (pathCopy.length === 0) {
    node = root;
  } else {
    parent = findNodeByPath(root, pathCopy.slice(0, -1));
    if (parent) {
      node = findNodeByPath(parent, [pathCopy[pathCopy.length - 1]]);
    }
  }

  const result = {
    node,
    parent,
    path: pathCopy,
    exists: !!node,
    canEdit: false,
    edits: []
  };

  // 检查是否可以编辑
  if (options.canEdit) {
    result.canEdit = true;

    // 如果节点存在，可以修改或删除
    if (node) {
      if (options.operation === 'delete') {
        result.edits = generateDeleteEdits(node, parent, text);
      } else if (options.operation === 'update' && options.value !== undefined) {
        result.edits = generateUpdateEdits(node, options.value, text, formattingOptions);
      }
    } else if (parent && options.operation === 'insert' && options.value !== undefined) {
      // 如果父节点存在，可以插入新值
      result.edits = generateInsertEdits(parent, pathCopy[pathCopy.length - 1], options.value, text, formattingOptions);
    }
  }

  return result;
}

/**
 * 生成删除编辑
 * @param {JSONNode} node - 要删除的节点
 * @param {JSONNode} parent - 父节点
 * @param {string} text - 原始文本
 * @returns {Array} 编辑数组
 * @private
 */
function generateDeleteEdits(node, parent, text) {
  const edits = [];

  if (parent && parent.type === JSONNodeType.OBJECT) {
    // 删除对象属性
    const propertyNode = parent.children.find(child =>
      child.type === JSONNodeType.PROPERTY && child.children.includes(node)
    );

    if (propertyNode) {
      edits.push({
        offset: propertyNode.offset,
        length: propertyNode.length,
        content: ''
      });
    }
  } else if (parent && parent.type === JSONNodeType.ARRAY) {
    // 删除数组元素
    const index = parent.children.indexOf(node);
    if (index >= 0) {
      edits.push({
        offset: node.offset,
        length: node.length,
        content: ''
      });
    }
  }

  return edits;
}

/**
 * 生成更新编辑
 * @param {JSONNode} node - 要更新的节点
 * @param {*} newValue - 新值
 * @param {string} text - 原始文本
 * @param {Object} formattingOptions - 格式化选项
 * @returns {Array} 编辑数组
 * @private
 */
function generateUpdateEdits(node, newValue, text, formattingOptions) {
  const edits = [];
  const newContent = JSON.stringify(newValue);

  edits.push({
    offset: node.offset,
    length: node.length,
    content: newContent
  });

  return edits;
}

/**
 * 生成插入编辑
 * @param {JSONNode} parent - 父节点
 * @param {string|number} key - 键或索引
 * @param {*} value - 值
 * @param {string} text - 原始文本
 * @param {Object} formattingOptions - 格式化选项
 * @returns {Array} 编辑数组
 * @private
 */
function generateInsertEdits(parent, key, value, text, formattingOptions) {
  const edits = [];

  if (parent.type === JSONNodeType.OBJECT) {
    // 插入对象属性
    const newProperty = `"${key}": ${JSON.stringify(value)}`;
    const insertOffset = parent.offset + parent.length - 1; // 在 } 之前插入

    edits.push({
      offset: insertOffset,
      length: 0,
      content: parent.children.length > 0 ? `, ${newProperty}` : newProperty
    });
  } else if (parent.type === JSONNodeType.ARRAY) {
    // 插入数组元素
    const newElement = JSON.stringify(value);
    const insertOffset = parent.offset + parent.length - 1; // 在 ] 之前插入

    edits.push({
      offset: insertOffset,
      length: 0,
      content: parent.children.length > 0 ? `, ${newElement}` : newElement
    });
  }

  return edits;
}

/**
 * JSON编辑工具类
 * @description 提供JSON编辑功能
 */
export class JSONEditor {
  constructor() {
    this.parser = new JSONParser();
  }

  /**
   * 查找节点
   * @param {string} text - JSON文本
   * @param {Array} path - 路径数组
   * @returns {Object} 查找结果
   */
  find(text, path) {
    const root = this.parser.parseTree(text, []);
    const node = findNodeByPath(root, path);

    return {
      node,
      exists: !!node,
      path: path.slice()
    };
  }

  /**
   * 设置值
   * @param {string} text - JSON文本
   * @param {Array} path - 路径数组
   * @param {*} value - 新值
   * @param {Object} options - 选项
   * @returns {Object} 编辑结果
   */
  setValue(text, path, value, options = {}) {
    return findAndEditJSON(text, path, {
      canEdit: true,
      operation: 'update',
      value
    }, options.formatting || {});
  }

  /**
   * 删除值
   * @param {string} text - JSON文本
   * @param {Array} path - 路径数组
   * @param {Object} options - 选项
   * @returns {Object} 编辑结果
   */
  deleteValue(text, path, options = {}) {
    return findAndEditJSON(text, path, {
      canEdit: true,
      operation: 'delete'
    }, options.formatting || {});
  }

  /**
   * 插入值
   * @param {string} text - JSON文本
   * @param {Array} path - 路径数组
   * @param {*} value - 新值
   * @param {Object} options - 选项
   * @returns {Object} 编辑结果
   */
  insertValue(text, path, value, options = {}) {
    return findAndEditJSON(text, path, {
      canEdit: true,
      operation: 'insert',
      value
    }, options.formatting || {});
  }

  /**
   * 应用编辑
   * @param {string} text - 原始文本
   * @param {Array} edits - 编辑数组
   * @returns {string} 编辑后的文本
   */
  applyEdits(text, edits) {
    // 按偏移量倒序排序，从后往前应用编辑
    const sortedEdits = edits.slice().sort((a, b) => b.offset - a.offset);

    let result = text;
    for (const edit of sortedEdits) {
      result = result.substring(0, edit.offset) +
               edit.content +
               result.substring(edit.offset + edit.length);
    }

    return result;
  }
}

// 扩展JSONParserUtils
JSONParserUtils.findNodeByPath = findNodeByPath;
JSONParserUtils.getJSONType = getJSONType;
JSONParserUtils.findAndEditJSON = findAndEditJSON;

// 创建默认JSON解析器和编辑器实例
export const jsonParser = new JSONParser();
export const jsonEditor = new JSONEditor();
