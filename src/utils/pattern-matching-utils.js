/**
 * 模式匹配工具函数
 * @description 重构自原始文件中的模式匹配和路径处理代码，对应第3500-3600行
 * @original 原始代码行 3500-3600
 */

import { join, relative, sep } from "path";
import { homedir } from "os";

/**
 * 路径分隔符
 * @original $5Q和vO变量
 */
const PATH_SEP = sep;

/**
 * 编辑工具名称
 * @original xO变量
 */
const EDIT_TOOL_NAME = "Edit";

/**
 * 读取工具名称
 * @original aJ变量
 */
const READ_TOOL_NAME = "Read";

/**
 * 检查路径是否在目录内（完整实现）
 * @param {string} path - 要检查的路径
 * @param {string} directory - 目录路径
 * @returns {boolean} 是否在目录内
 * @original ki函数的完整实现
 */
export function isPathWithinDirectory(path, directory) {
  // 规范化路径，处理macOS的特殊路径
  const normalizedPath = path
    .replace(/^\/private\/var\//, "/var/")
    .replace(/^\/private\/tmp(\/|$)/, "/tmp$1");
  
  const normalizedDirectory = directory
    .replace(/^\/private\/var\//, "/var/")
    .replace(/^\/private\/tmp(\/|$)/, "/tmp$1");
  
  // 检查路径是否以目录开头
  if (!normalizedPath.startsWith(normalizedDirectory)) {
    return false;
  }
  
  // 检查分隔符，确保是真正的子路径
  const nextChar = normalizedPath[normalizedDirectory.length];
  if (nextChar === undefined || nextChar === PATH_SEP) {
    return true;
  }
  
  return false;
}

/**
 * 根据源获取根路径
 * @param {string} source - 源类型
 * @returns {string} 根路径
 * @original L5Q函数
 */
export function getRootPathBySource(source) {
  switch (source) {
    case "cliArg":
    case "command":
      return resolveWorkingDirectory(getUserHomeDirectory());
    case "userSettings":
    case "policySettings":
    case "projectSettings":
    case "localSettings":
    case "flagSettings":
      return getConfigRootPath(source);
    default:
      return process.cwd();
  }
}

/**
 * 创建相对路径模式
 * @param {string} pattern - 模式字符串
 * @returns {string} 相对路径模式
 * @original Er1函数
 */
export function createRelativePattern(pattern) {
  return join(PATH_SEP, pattern);
}

/**
 * 解析模式路径
 * @param {Object} options - 解析选项
 * @returns {string|null} 解析后的路径或null
 * @original M5Q函数
 */
export function resolvePatternPath({ patternRoot, pattern, rootPath }) {
  const fullPath = join(patternRoot, pattern);
  
  if (patternRoot === rootPath) {
    return createRelativePattern(pattern);
  } else if (fullPath.startsWith(`${rootPath}${PATH_SEP}`)) {
    const relativePath = fullPath.slice(rootPath.length);
    return createRelativePattern(relativePath);
  } else {
    const relativePath = relative(rootPath, patternRoot);
    
    if (!relativePath || relativePath.startsWith(`..${PATH_SEP}`) || relativePath === "..") {
      return null;
    } else {
      const combinedPath = join(relativePath, pattern);
      return createRelativePattern(combinedPath);
    }
  }
}

/**
 * 构建模式列表
 * @param {Map} patternMap - 模式映射
 * @param {string} rootPath - 根路径
 * @returns {Array} 模式列表
 * @original LK1函数
 */
export function buildPatternList(patternMap, rootPath) {
  const patterns = new Set(patternMap.get(null) ?? []);
  
  for (const [patternRoot, patternList] of patternMap.entries()) {
    if (patternRoot === null) continue;
    
    for (const pattern of patternList) {
      const resolvedPattern = resolvePatternPath({
        patternRoot,
        pattern,
        rootPath
      });
      
      if (resolvedPattern) {
        patterns.add(resolvedPattern);
      }
    }
  }
  
  return Array.from(patterns);
}

/**
 * 获取忽略模式映射
 * @param {Object} context - 上下文对象
 * @returns {Map} 忽略模式映射
 * @original _i函数
 */
export function getIgnorePatternMap(context) {
  const denyPatterns = getToolContentPatterns(context, "read", "deny");
  const patternMap = new Map();
  
  // 转换为数组格式
  for (const [root, patternSet] of denyPatterns.entries()) {
    patternMap.set(root, Array.from(patternSet.keys()));
  }
  
  // 添加项目配置中的忽略模式
  const projectConfig = getProjectConfig();
  const ignorePatterns = projectConfig.ignorePatterns;
  
  if (ignorePatterns && ignorePatterns.length > 0) {
    for (const pattern of ignorePatterns) {
      const { relativePattern, root } = parsePatternPath(pattern, "projectSettings");
      const existingPatterns = patternMap.get(root);
      
      if (existingPatterns === undefined) {
        patternMap.set(root, [relativePattern]);
      } else {
        existingPatterns.push(relativePattern);
      }
    }
  }
  
  return patternMap;
}

/**
 * 解析模式路径
 * @param {string} pattern - 模式字符串
 * @param {string} source - 源类型
 * @returns {Object} 解析结果
 * @original xIA函数
 */
export function parsePatternPath(pattern, source) {
  if (pattern.startsWith(`${PATH_SEP}${PATH_SEP}`)) {
    // 以 // 开头，相对于根目录
    return {
      relativePattern: pattern.slice(1),
      root: PATH_SEP
    };
  } else if (pattern.startsWith(`~${PATH_SEP}`)) {
    // 以 ~/ 开头，相对于用户主目录
    return {
      relativePattern: pattern.slice(1),
      root: homedir()
    };
  } else if (pattern.startsWith(PATH_SEP)) {
    // 以 / 开头，绝对路径
    return {
      relativePattern: pattern,
      root: getRootPathBySource(source)
    };
  }
  
  // 相对路径
  return {
    relativePattern: pattern,
    root: null
  };
}

/**
 * 获取工具内容模式
 * @param {Object} context - 上下文对象
 * @param {string} toolType - 工具类型
 * @param {string} behavior - 行为类型
 * @returns {Map} 内容模式映射
 * @original vIA函数
 */
export function getToolContentPatterns(context, toolType, behavior) {
  const toolName = (() => {
    switch (toolType) {
      case "edit":
        return EDIT_TOOL_NAME;
      case "read":
        return READ_TOOL_NAME;
      default:
        return toolType;
    }
  })();
  
  const contentRules = getToolContentRulesByName(context, toolName, behavior);
  const patternMap = new Map();
  
  for (const [content, rule] of contentRules.entries()) {
    const { relativePattern, root } = parsePatternPath(content, rule.source);
    
    if (!patternMap.has(root)) {
      patternMap.set(root, new Map());
    }
    
    patternMap.get(root).set(relativePattern, rule);
  }
  
  return patternMap;
}

/**
 * 模式匹配工具类
 * @description 提供完整的模式匹配功能
 */
export class PatternMatcher {
  constructor() {
    this.patterns = new Map();
    this.compiledPatterns = new Map();
  }

  /**
   * 添加模式
   * @param {string} name - 模式名称
   * @param {string} pattern - 模式字符串
   * @param {Object} options - 选项
   */
  addPattern(name, pattern, options = {}) {
    this.patterns.set(name, {
      pattern,
      options,
      compiled: this.compilePattern(pattern, options)
    });
  }

  /**
   * 移除模式
   * @param {string} name - 模式名称
   * @returns {boolean} 是否移除成功
   */
  removePattern(name) {
    return this.patterns.delete(name);
  }

  /**
   * 编译模式为正则表达式
   * @param {string} pattern - 模式字符串
   * @param {Object} options - 选项
   * @returns {RegExp} 编译后的正则表达式
   * @private
   */
  compilePattern(pattern, options = {}) {
    let regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.')
      .replace(/\[([^\]]+)\]/g, '[$1]');
    
    if (options.exactMatch) {
      regexPattern = `^${regexPattern}$`;
    }
    
    const flags = options.caseSensitive ? '' : 'i';
    return new RegExp(regexPattern, flags);
  }

  /**
   * 测试路径是否匹配模式
   * @param {string} path - 路径
   * @param {string} patternName - 模式名称
   * @returns {boolean} 是否匹配
   */
  test(path, patternName) {
    const patternInfo = this.patterns.get(patternName);
    if (!patternInfo) {
      return false;
    }
    
    return patternInfo.compiled.test(path);
  }

  /**
   * 测试路径是否匹配任何模式
   * @param {string} path - 路径
   * @returns {Array} 匹配的模式名称数组
   */
  testAll(path) {
    const matches = [];
    
    for (const [name, patternInfo] of this.patterns.entries()) {
      if (patternInfo.compiled.test(path)) {
        matches.push(name);
      }
    }
    
    return matches;
  }

  /**
   * 获取所有模式
   * @returns {Array} 模式信息数组
   */
  getAllPatterns() {
    return Array.from(this.patterns.entries()).map(([name, info]) => ({
      name,
      pattern: info.pattern,
      options: info.options
    }));
  }

  /**
   * 清空所有模式
   */
  clear() {
    this.patterns.clear();
    this.compiledPatterns.clear();
  }

  /**
   * 批量添加模式
   * @param {Object} patterns - 模式对象
   */
  addPatterns(patterns) {
    for (const [name, config] of Object.entries(patterns)) {
      if (typeof config === 'string') {
        this.addPattern(name, config);
      } else {
        this.addPattern(name, config.pattern, config.options);
      }
    }
  }

  /**
   * 导出模式配置
   * @returns {Object} 模式配置
   */
  exportConfig() {
    const config = {};
    
    for (const [name, info] of this.patterns.entries()) {
      config[name] = {
        pattern: info.pattern,
        options: info.options
      };
    }
    
    return config;
  }

  /**
   * 导入模式配置
   * @param {Object} config - 模式配置
   */
  importConfig(config) {
    this.clear();
    this.addPatterns(config);
  }
}

// 辅助函数

/**
 * 获取用户主目录
 * @returns {string} 用户主目录路径
 * @original x9函数的实现（推测）
 */
function getUserHomeDirectory() {
  return homedir();
}

/**
 * 解析工作目录
 * @param {string} path - 路径
 * @returns {string} 解析后的路径
 * @original WZ函数的实现（推测）
 */
function resolveWorkingDirectory(path) {
  return path;
}

/**
 * 获取配置根路径
 * @param {string} source - 配置源
 * @returns {string} 配置根路径
 * @original MK1函数的实现（推测）
 */
function getConfigRootPath(source) {
  // @todo: 实现MK1函数的实际逻辑
  switch (source) {
    case "userSettings":
      return join(getUserHomeDirectory(), ".claude");
    case "projectSettings":
    case "localSettings":
      return join(process.cwd(), ".claude");
    default:
      return process.cwd();
  }
}

/**
 * 获取项目配置
 * @returns {Object} 项目配置
 * @original t9函数的实现（推测）
 */
function getProjectConfig() {
  // @todo: 实现t9函数的实际逻辑
  return {
    ignorePatterns: []
  };
}

/**
 * 根据工具名称获取内容规则
 * @param {Object} context - 上下文对象
 * @param {string} toolName - 工具名称
 * @param {string} behavior - 行为类型
 * @returns {Map} 内容规则映射
 * @original zr1函数的实现（推测）
 */
function getToolContentRulesByName(context, toolName, behavior) {
  // @todo: 实现zr1函数的实际逻辑
  return new Map();
}

// 创建默认模式匹配器实例
export const defaultPatternMatcher = new PatternMatcher();
