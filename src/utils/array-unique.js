/**
 * 数组去重工具函数
 * @description 重构自原始文件中的数组去重函数，对应第1953-2001行
 * @original 原始代码行 1953-2001
 */

import SetCache from './set-cache.js';
import { arrayIncludes } from './array-processing.js';

/**
 * 大数组阈值
 * @original var OoB = 200;
 */
const LARGE_ARRAY_SIZE = 200;

/**
 * 基础数组去重函数
 * @param {Array} array - 要去重的数组
 * @param {Function} iteratee - 迭代函数
 * @param {Function} comparator - 比较函数
 * @returns {Array} 去重后的数组
 * @original function ToB(A, B, Q) { ... }
 */
export function baseUniq(array, iteratee, comparator) {
  let index = -1;
  let includes = arrayIncludes;
  let isCommon = true;
  
  const { length } = array;
  const result = [];
  let seen = result;
  
  if (comparator) {
    isCommon = false;
    includes = cacheHas;
  } else if (length >= LARGE_ARRAY_SIZE) {
    const set = iteratee ? null : createSet(array);
    if (set) {
      return setToArray(set);
    }
    isCommon = false;
    includes = cacheHas;
    seen = new SetCache();
  } else {
    seen = iteratee ? [] : result;
  }
  
  outer:
  while (++index < length) {
    let value = array[index];
    const computed = iteratee ? iteratee(value) : value;
    
    value = (comparator || value !== 0) ? value : 0;
    if (isCommon && computed === computed) {
      let seenIndex = seen.length;
      while (seenIndex--) {
        if (seen[seenIndex] === computed) {
          continue outer;
        }
      }
      if (iteratee) {
        seen.push(computed);
      }
      result.push(value);
    } else if (!includes(seen, computed, comparator)) {
      if (seen !== result) {
        seen.push(computed);
      }
      result.push(value);
    }
  }
  
  return result;
}

/**
 * 数组去重（使用迭代函数）
 * @param {Array} array - 要去重的数组
 * @param {Function} iteratee - 迭代函数
 * @returns {Array} 去重后的数组
 * @original function PoB(A, B) { return A && A.length ? $j0(A, nq(B, 2)) : []; }
 */
export function uniqBy(array, iteratee) {
  return (array && array.length) ? baseUniq(array, getIteratee(iteratee, 2)) : [];
}

/**
 * 数组去重（简单版本）
 * @param {Array} array - 要去重的数组
 * @returns {Array} 去重后的数组
 */
export function uniq(array) {
  return (array && array.length) ? baseUniq(array) : [];
}

/**
 * 数组去重（使用比较函数）
 * @param {Array} array - 要去重的数组
 * @param {Function} comparator - 比较函数
 * @returns {Array} 去重后的数组
 */
export function uniqWith(array, comparator) {
  comparator = typeof comparator == 'function' ? comparator : undefined;
  return (array && array.length) ? baseUniq(array, undefined, comparator) : [];
}

/**
 * 将两个数组组合成对象
 * @param {Array} props - 属性数组
 * @param {Array} values - 值数组
 * @param {Function} assignFunc - 赋值函数
 * @returns {Object} 组合后的对象
 * @original function SoB(A, B, Q) { var D = -1, Z = A.length, G = B.length, F = {}; while (++D < Z) { var I = D < G ? B[D] : void 0; Q(F, A[D], I); } return F; }
 */
export function baseZipObject(props, values, assignFunc) {
  let index = -1;
  const length = props.length;
  const valsLength = values.length;
  const result = {};
  
  while (++index < length) {
    const value = index < valsLength ? values[index] : undefined;
    assignFunc(result, props[index], value);
  }
  
  return result;
}

/**
 * 将两个数组组合成对象
 * @param {Array} props - 属性数组
 * @param {Array} values - 值数组
 * @returns {Object} 组合后的对象
 * @original function joB(A, B) { return qj0(A || [], B || [], Pj); }
 */
export function zipObject(props, values) {
  return baseZipObject(props || [], values || [], assignValue);
}

/**
 * 将两个数组组合成对象（深度赋值）
 * @param {Array} props - 属性数组
 * @param {Array} values - 值数组
 * @returns {Object} 组合后的对象
 */
export function zipObjectDeep(props, values) {
  return baseZipObject(props || [], values || [], baseSet);
}

/**
 * 多个数组的交集
 * @param {...Array} arrays - 数组
 * @returns {Array} 交集数组
 */
export function intersection(...arrays) {
  const mapped = arrays.map(castArrayLikeObject);
  return (mapped.length && mapped[0] === arrays[0])
    ? baseIntersection(mapped)
    : [];
}

/**
 * 多个数组的交集（使用迭代函数）
 * @param {...Array} arrays - 数组
 * @returns {Array} 交集数组
 */
export function intersectionBy(...arrays) {
  let iteratee = last(arrays);
  const mapped = arrays.map(castArrayLikeObject);
  
  if (iteratee === last(mapped)) {
    iteratee = undefined;
  } else {
    mapped.pop();
  }
  
  return (mapped.length && mapped[0] === arrays[0])
    ? baseIntersection(mapped, getIteratee(iteratee, 2))
    : [];
}

/**
 * 多个数组的交集（使用比较函数）
 * @param {...Array} arrays - 数组
 * @returns {Array} 交集数组
 */
export function intersectionWith(...arrays) {
  let comparator = last(arrays);
  const mapped = arrays.map(castArrayLikeObject);
  
  comparator = typeof comparator == 'function' ? comparator : undefined;
  if (comparator) {
    mapped.pop();
  }
  
  return (mapped.length && mapped[0] === arrays[0])
    ? baseIntersection(mapped, undefined, comparator)
    : [];
}

/**
 * 多个数组的并集
 * @param {...Array} arrays - 数组
 * @returns {Array} 并集数组
 */
export function union(...arrays) {
  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true));
}

/**
 * 多个数组的并集（使用迭代函数）
 * @param {...Array} arrays - 数组
 * @returns {Array} 并集数组
 */
export function unionBy(...arrays) {
  let iteratee = last(arrays);
  if (isArrayLikeObject(iteratee)) {
    iteratee = undefined;
  } else {
    arrays.pop();
  }
  
  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true), getIteratee(iteratee, 2));
}

/**
 * 多个数组的并集（使用比较函数）
 * @param {...Array} arrays - 数组
 * @returns {Array} 并集数组
 */
export function unionWith(...arrays) {
  let comparator = last(arrays);
  comparator = typeof comparator == 'function' ? comparator : undefined;
  if (comparator) {
    arrays.pop();
  }
  
  return baseUniq(baseFlatten(arrays, 1, isArrayLikeObject, true), undefined, comparator);
}

// 辅助函数

/**
 * 检查SetCache是否包含值
 * @param {SetCache} cache - 缓存
 * @param {*} key - 键
 * @returns {boolean} 是否包含
 */
function cacheHas(cache, key) {
  return cache.has(key);
}

/**
 * 创建Set（如果支持的话）
 * @param {Array} array - 数组
 * @returns {Set|null} Set实例或null
 */
function createSet(array) {
  // @todo: 实现wj0函数的逻辑
  return (Set && (1 / setToArray(new Set([,-0]))[1]) == Infinity) ? null : function(array) {
    return new Set(array);
  }(array);
}

/**
 * 将Set转换为数组
 * @param {Set} set - Set实例
 * @returns {Array} 数组
 */
function setToArray(set) {
  let index = -1;
  const result = Array(set.size);
  
  set.forEach((value) => {
    result[++index] = value;
  });
  return result;
}

/**
 * 获取迭代函数
 * @param {*} value - 值
 * @param {number} arity - 参数数量
 * @returns {Function} 迭代函数
 */
function getIteratee(value, arity) {
  // @todo: 实现nq函数的逻辑
  return baseIteratee(value, arity);
}

/**
 * 基础迭代函数
 * @param {*} value - 值
 * @param {number} arity - 参数数量
 * @returns {Function} 迭代函数
 */
function baseIteratee(value, arity) {
  if (typeof value == 'function') {
    return value;
  }
  if (value == null) {
    return identity;
  }
  if (typeof value == 'object') {
    return Array.isArray(value)
      ? baseMatchesProperty(value[0], value[1])
      : baseMatches(value);
  }
  return property(value);
}

/**
 * 恒等函数
 * @param {*} value - 值
 * @returns {*} 原值
 */
function identity(value) {
  return value;
}

/**
 * 基础属性匹配函数
 * @param {string} path - 路径
 * @param {*} srcValue - 源值
 * @returns {Function} 匹配函数
 */
function baseMatchesProperty(path, srcValue) {
  return function(object) {
    return baseGet(object, path) === srcValue;
  };
}

/**
 * 基础对象匹配函数
 * @param {Object} source - 源对象
 * @returns {Function} 匹配函数
 */
function baseMatches(source) {
  return function(object) {
    return baseIsMatch(object, source);
  };
}

/**
 * 属性获取函数
 * @param {string} path - 路径
 * @returns {Function} 获取函数
 */
function property(path) {
  return isKey(path) ? baseProperty(path) : basePropertyDeep(path);
}

/**
 * 赋值函数
 * @param {Object} object - 对象
 * @param {string} key - 键
 * @param {*} value - 值
 */
function assignValue(object, key, value) {
  const objValue = object[key];
  if (!(Object.prototype.hasOwnProperty.call(object, key) && eq(objValue, value)) ||
      (value === undefined && !(key in object))) {
    object[key] = value;
  }
}

/**
 * 基础设置函数
 * @param {Object} object - 对象
 * @param {string} path - 路径
 * @param {*} value - 值
 * @returns {Object} 对象
 */
function baseSet(object, path, value) {
  // 简化实现
  const keys = Array.isArray(path) ? path : path.split('.');
  let current = object;
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {};
    }
    current = current[key];
  }
  
  current[keys[keys.length - 1]] = value;
  return object;
}

/**
 * 获取数组最后一个元素
 * @param {Array} array - 数组
 * @returns {*} 最后一个元素
 */
function last(array) {
  const length = array == null ? 0 : array.length;
  return length ? array[length - 1] : undefined;
}

/**
 * 将值转换为类数组对象
 * @param {*} value - 值
 * @returns {Object} 类数组对象
 */
function castArrayLikeObject(value) {
  return isArrayLikeObject(value) ? value : [];
}

/**
 * 检查值是否为类数组对象
 * @param {*} value - 值
 * @returns {boolean} 是否为类数组对象
 */
function isArrayLikeObject(value) {
  return isObjectLike(value) && isArrayLike(value);
}

/**
 * 检查值是否为类对象
 * @param {*} value - 值
 * @returns {boolean} 是否为类对象
 */
function isObjectLike(value) {
  return value != null && typeof value == 'object';
}

/**
 * 检查值是否类似数组
 * @param {*} value - 值
 * @returns {boolean} 是否类似数组
 */
function isArrayLike(value) {
  return value != null && typeof value != 'function' && isLength(value.length);
}

/**
 * 检查值是否为有效长度
 * @param {*} value - 值
 * @returns {boolean} 是否为有效长度
 */
function isLength(value) {
  return typeof value == 'number' &&
    value > -1 && value % 1 == 0 && value <= Number.MAX_SAFE_INTEGER;
}

/**
 * 基础数组扁平化
 * @param {Array} array - 数组
 * @param {number} depth - 深度
 * @param {Function} predicate - 判断函数
 * @param {boolean} isStrict - 是否严格
 * @returns {Array} 扁平化数组
 */
function baseFlatten(array, depth, predicate, isStrict) {
  // 简化实现
  const result = [];
  
  for (const item of array) {
    if (Array.isArray(item) && depth > 0) {
      result.push(...baseFlatten(item, depth - 1, predicate, isStrict));
    } else if (!isStrict || predicate(item)) {
      result.push(item);
    }
  }
  
  return result;
}

/**
 * 基础交集函数
 * @param {Array} arrays - 数组集合
 * @param {Function} iteratee - 迭代函数
 * @param {Function} comparator - 比较函数
 * @returns {Array} 交集数组
 */
function baseIntersection(arrays, iteratee, comparator) {
  const includes = comparator ? arrayIncludesWith : arrayIncludes;
  const length = arrays[0].length;
  const othLength = arrays.length;
  const caches = Array(othLength);
  const result = [];
  
  let array;
  let maxLength = Infinity;
  let othIndex = othLength;
  
  while (othIndex--) {
    array = arrays[othIndex];
    if (othIndex && iteratee) {
      array = array.map((value) => iteratee(value));
    }
    maxLength = Math.min(array.length, maxLength);
    caches[othIndex] = !comparator && (iteratee || (length >= 120 && array.length >= 120))
      ? new SetCache(othIndex && array)
      : undefined;
  }
  
  array = arrays[0];
  let index = -1;
  const seen = caches[0];
  
  outer:
  while (++index < length && result.length < maxLength) {
    let value = array[index];
    const computed = iteratee ? iteratee(value) : value;
    
    value = (comparator || value !== 0) ? value : 0;
    if (!(seen
          ? cacheHas(seen, computed)
          : includes(result, computed, comparator)
        )) {
      othIndex = othLength;
      while (--othIndex) {
        const cache = caches[othIndex];
        if (!(cache
              ? cacheHas(cache, computed)
              : includes(arrays[othIndex], computed, comparator))
            ) {
          continue outer;
        }
      }
      if (seen) {
        seen.push(computed);
      }
      result.push(value);
    }
  }
  
  return result;
}

/**
 * 数组包含函数（使用比较函数）
 * @param {Array} array - 数组
 * @param {*} target - 目标值
 * @param {Function} comparator - 比较函数
 * @returns {boolean} 是否包含
 */
function arrayIncludesWith(array, target, comparator) {
  let index = -1;
  const length = array.length;
  
  while (++index < length) {
    if (comparator(target, array[index])) {
      return true;
    }
  }
  return false;
}

/**
 * 其他辅助函数的简化实现
 */
function baseGet(object, path) {
  // 简化实现
  return object[path];
}

function baseIsMatch(object, source) {
  // 简化实现
  return JSON.stringify(object) === JSON.stringify(source);
}

function isKey(value) {
  // 简化实现
  return typeof value === 'string' || typeof value === 'number';
}

function baseProperty(key) {
  return function(object) {
    return object[key];
  };
}

function basePropertyDeep(path) {
  return function(object) {
    return baseGet(object, path);
  };
}

function eq(value, other) {
  return value === other || (value !== value && other !== other);
}
