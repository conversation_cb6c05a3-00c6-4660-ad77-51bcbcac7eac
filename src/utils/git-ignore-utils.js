/**
 * Git忽略文件管理工具
 * @description 重构自原始文件中的Git忽略文件处理代码，对应第4822-4850行
 * @original 原始代码行 4822-4850
 */

import { join } from "path";
import { homedir } from "os";
import { isDirectoryInsideGitRepository } from './git-utils.js';

/**
 * 检查文件是否被Git忽略
 * @param {string} filePath - 文件路径
 * @param {string} cwd - 工作目录（可选）
 * @returns {Promise<boolean>} 是否被忽略
 * @original d5Q函数
 */
export async function isFileIgnoredByGit(filePath, cwd) {
  const { code } = await executeCommandInDirectory("git", ["check-ignore", filePath], {
    preserveOutputOnError: false,
    cwd
  });
  return code === 0;
}

/**
 * 获取全局Git忽略文件路径
 * @returns {string} 全局忽略文件路径
 * @original c5Q函数
 */
export function getGlobalGitIgnorePath() {
  return join(getUserHomeDirectory(), ".config", "git", "ignore");
}

/**
 * 添加文件到Git忽略
 * @param {string} fileName - 文件名
 * @param {string} baseDirectory - 基础目录（可选）
 * @returns {Promise<void>}
 * @original hi函数
 */
export async function addToGitIgnore(fileName, baseDirectory = getCurrentWorkingDirectory()) {
  try {
    // 检查是否在Git仓库内
    if (!(await isDirectoryInsideGitRepository(baseDirectory))) {
      return;
    }

    const pattern = `**/${fileName}`;
    
    // 检查文件是否已被忽略
    if (await isFileIgnoredByGit(fileName, baseDirectory)) {
      return;
    }

    const globalIgnorePath = getGlobalGitIgnorePath();
    const fs = getFileSystem();
    const configDir = join(getUserHomeDirectory(), ".config", "git");

    // 确保配置目录存在
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    // 添加到全局忽略文件
    if (fs.existsSync(globalIgnorePath)) {
      fs.appendFileSync(globalIgnorePath, `\n${pattern}\n`);
    } else {
      fs.writeFileSync(globalIgnorePath, `${pattern}\n`);
    }
  } catch (error) {
    logError(error instanceof Error ? error : new Error(String(error)));
  }
}

/**
 * Git忽略管理工具类
 * @description 提供完整的Git忽略文件管理功能
 */
export class GitIgnoreManager {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 30000; // 30秒缓存
  }

  /**
   * 检查文件是否被忽略
   * @param {string} filePath - 文件路径
   * @param {string} cwd - 工作目录
   * @returns {Promise<boolean>} 是否被忽略
   */
  async isIgnored(filePath, cwd = getCurrentWorkingDirectory()) {
    const cacheKey = `ignored:${filePath}:${cwd}`;
    const cached = this.getFromCache(cacheKey);
    
    if (cached !== null) {
      return cached;
    }

    const result = await isFileIgnoredByGit(filePath, cwd);
    this.setCache(cacheKey, result);
    
    return result;
  }

  /**
   * 添加模式到忽略文件
   * @param {string} pattern - 忽略模式
   * @param {Object} options - 选项
   * @returns {Promise<boolean>} 是否添加成功
   */
  async addPattern(pattern, options = {}) {
    const {
      global = true,
      local = false,
      baseDirectory = getCurrentWorkingDirectory()
    } = options;

    try {
      if (global) {
        await this.addToGlobalIgnore(pattern);
      }

      if (local) {
        await this.addToLocalIgnore(pattern, baseDirectory);
      }

      // 清除相关缓存
      this.clearCache();
      
      return true;
    } catch (error) {
      logError(error);
      return false;
    }
  }

  /**
   * 添加到全局忽略文件
   * @param {string} pattern - 忽略模式
   * @returns {Promise<void>}
   * @private
   */
  async addToGlobalIgnore(pattern) {
    const globalIgnorePath = getGlobalGitIgnorePath();
    const fs = getFileSystem();
    const configDir = join(getUserHomeDirectory(), ".config", "git");

    // 确保配置目录存在
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    // 检查模式是否已存在
    if (fs.existsSync(globalIgnorePath)) {
      const content = fs.readFileSync(globalIgnorePath, 'utf8');
      if (content.includes(pattern)) {
        return; // 模式已存在
      }
      fs.appendFileSync(globalIgnorePath, `\n${pattern}\n`);
    } else {
      fs.writeFileSync(globalIgnorePath, `${pattern}\n`);
    }
  }

  /**
   * 添加到本地忽略文件
   * @param {string} pattern - 忽略模式
   * @param {string} baseDirectory - 基础目录
   * @returns {Promise<void>}
   * @private
   */
  async addToLocalIgnore(pattern, baseDirectory) {
    const localIgnorePath = join(baseDirectory, ".gitignore");
    const fs = getFileSystem();

    // 检查模式是否已存在
    if (fs.existsSync(localIgnorePath)) {
      const content = fs.readFileSync(localIgnorePath, 'utf8');
      if (content.includes(pattern)) {
        return; // 模式已存在
      }
      fs.appendFileSync(localIgnorePath, `\n${pattern}\n`);
    } else {
      fs.writeFileSync(localIgnorePath, `${pattern}\n`);
    }
  }

  /**
   * 移除忽略模式
   * @param {string} pattern - 忽略模式
   * @param {Object} options - 选项
   * @returns {Promise<boolean>} 是否移除成功
   */
  async removePattern(pattern, options = {}) {
    const {
      global = true,
      local = false,
      baseDirectory = getCurrentWorkingDirectory()
    } = options;

    try {
      let removed = false;

      if (global) {
        removed = await this.removeFromGlobalIgnore(pattern) || removed;
      }

      if (local) {
        removed = await this.removeFromLocalIgnore(pattern, baseDirectory) || removed;
      }

      if (removed) {
        this.clearCache();
      }

      return removed;
    } catch (error) {
      logError(error);
      return false;
    }
  }

  /**
   * 从全局忽略文件移除模式
   * @param {string} pattern - 忽略模式
   * @returns {Promise<boolean>} 是否移除成功
   * @private
   */
  async removeFromGlobalIgnore(pattern) {
    const globalIgnorePath = getGlobalGitIgnorePath();
    const fs = getFileSystem();

    if (!fs.existsSync(globalIgnorePath)) {
      return false;
    }

    const content = fs.readFileSync(globalIgnorePath, 'utf8');
    const lines = content.split('\n');
    const filteredLines = lines.filter(line => line.trim() !== pattern);

    if (filteredLines.length !== lines.length) {
      fs.writeFileSync(globalIgnorePath, filteredLines.join('\n'));
      return true;
    }

    return false;
  }

  /**
   * 从本地忽略文件移除模式
   * @param {string} pattern - 忽略模式
   * @param {string} baseDirectory - 基础目录
   * @returns {Promise<boolean>} 是否移除成功
   * @private
   */
  async removeFromLocalIgnore(pattern, baseDirectory) {
    const localIgnorePath = join(baseDirectory, ".gitignore");
    const fs = getFileSystem();

    if (!fs.existsSync(localIgnorePath)) {
      return false;
    }

    const content = fs.readFileSync(localIgnorePath, 'utf8');
    const lines = content.split('\n');
    const filteredLines = lines.filter(line => line.trim() !== pattern);

    if (filteredLines.length !== lines.length) {
      fs.writeFileSync(localIgnorePath, filteredLines.join('\n'));
      return true;
    }

    return false;
  }

  /**
   * 获取忽略模式列表
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 忽略模式列表
   */
  async getPatterns(options = {}) {
    const {
      global = true,
      local = true,
      baseDirectory = getCurrentWorkingDirectory()
    } = options;

    const result = {
      global: [],
      local: []
    };

    try {
      if (global) {
        result.global = await this.getGlobalPatterns();
      }

      if (local) {
        result.local = await this.getLocalPatterns(baseDirectory);
      }
    } catch (error) {
      logError(error);
    }

    return result;
  }

  /**
   * 获取全局忽略模式
   * @returns {Promise<Array>} 全局忽略模式数组
   * @private
   */
  async getGlobalPatterns() {
    const globalIgnorePath = getGlobalGitIgnorePath();
    const fs = getFileSystem();

    if (!fs.existsSync(globalIgnorePath)) {
      return [];
    }

    const content = fs.readFileSync(globalIgnorePath, 'utf8');
    return content.split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'));
  }

  /**
   * 获取本地忽略模式
   * @param {string} baseDirectory - 基础目录
   * @returns {Promise<Array>} 本地忽略模式数组
   * @private
   */
  async getLocalPatterns(baseDirectory) {
    const localIgnorePath = join(baseDirectory, ".gitignore");
    const fs = getFileSystem();

    if (!fs.existsSync(localIgnorePath)) {
      return [];
    }

    const content = fs.readFileSync(localIgnorePath, 'utf8');
    return content.split('\n')
      .map(line => line.trim())
      .filter(line => line && !line.startsWith('#'));
  }

  /**
   * 批量检查文件是否被忽略
   * @param {Array} filePaths - 文件路径数组
   * @param {string} cwd - 工作目录
   * @returns {Promise<Array>} 检查结果数组
   */
  async batchCheck(filePaths, cwd = getCurrentWorkingDirectory()) {
    const results = [];

    for (const filePath of filePaths) {
      try {
        const ignored = await this.isIgnored(filePath, cwd);
        results.push({
          filePath,
          ignored,
          error: null
        });
      } catch (error) {
        results.push({
          filePath,
          ignored: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * 从缓存获取结果
   * @param {string} key - 缓存键
   * @returns {*} 缓存的结果或null
   * @private
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.result;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} result - 结果
   * @private
   */
  setCache(key, result) {
    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear();
  }
}

// 辅助函数

/**
 * 在指定目录执行命令
 * @param {string} command - 命令
 * @param {Array} args - 参数
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 执行结果
 * @original j5函数的实现（推测）
 */
async function executeCommandInDirectory(command, args, options = {}) {
  const { spawn } = require('child_process');
  
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      ...options
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      resolve({ code, stdout, stderr });
    });
    
    child.on('error', reject);
  });
}

/**
 * 获取用户主目录
 * @returns {string} 用户主目录
 * @original KYA函数的实现（推测）
 */
function getUserHomeDirectory() {
  return homedir();
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 * @original x1函数的实现（推测）
 */
function getFileSystem() {
  return require('fs');
}

/**
 * 记录错误
 * @param {Error} error - 错误对象
 * @original T1函数的实现（推测）
 */
function logError(error) {
  console.error('Git ignore error:', error);
}

// 创建默认Git忽略管理器实例
export const gitIgnoreManager = new GitIgnoreManager();
