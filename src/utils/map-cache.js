/**
 * Map Cache实现
 * @description 重构自原始文件中的Map Cache实现，对应第1490-1531行
 * @original 原始代码行 1490-1531
 */

import Hash from './hash-map.js';
import ListCache from './list-cache.js';

/**
 * 检查键是否适合用于Hash存储
 * @param {*} key - 要检查的键
 * @returns {boolean} 是否适合Hash存储
 * @original function miB(A) { var B = typeof A; return B == "string" || B == "number" || B == "symbol" || B == "boolean" ? A !== "__proto__" : A === null; }
 */
function isKeyHashable(key) {
  const type = typeof key;
  return (type == "string" || type == "number" || type == "symbol" || type == "boolean") 
    ? (key !== "__proto__") 
    : (key === null);
}

/**
 * 获取适合指定键的数据存储
 * @param {Object} mapCache - Map Cache实例
 * @param {*} key - 键
 * @returns {Object} 对应的存储对象
 * @original function diB(A, B) { var Q = A.__data__; return NP0(B) ? Q[typeof B == "string" ? "string" : "hash"] : Q.map; }
 */
function getMapData(mapCache, key) {
  const data = mapCache.__data__;
  return isKeyHashable(key) 
    ? data[typeof key == "string" ? "string" : "hash"] 
    : data.map;
}

/**
 * Map Cache构造函数
 * @param {Array} entries - 初始键值对数组
 * @original function fc(A) { ... } (需要从上下文推断)
 */
export function MapCache(entries) {
  let index = -1;
  const length = entries == null ? 0 : entries.length;
  
  this.clear();
  while (++index < length) {
    const entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}

/**
 * 清空Map Cache
 * @original function uiB() { this.size = 0, this.__data__ = { hash: new Su1(), map: new (yj || jj)(), string: new Su1() }; }
 */
function mapCacheClear() {
  this.size = 0;
  this.__data__ = {
    hash: new Hash(),
    map: new (Map || ListCache)(), // 使用原生Map或ListCache作为fallback
    string: new Hash()
  };
}

/**
 * 删除Map Cache中的键值对
 * @param {*} key - 要删除的键
 * @returns {boolean} 是否删除成功
 * @original function ciB(A) { var B = kj(this, A).delete(A); return this.size -= B ? 1 : 0, B; }
 */
function mapCacheDelete(key) {
  const result = getMapData(this, key).delete(key);
  this.size -= result ? 1 : 0;
  return result;
}

/**
 * 获取Map Cache中指定键的值
 * @param {*} key - 要获取的键
 * @returns {*} 键对应的值
 * @original function liB(A) { return kj(this, A).get(A); }
 */
function mapCacheGet(key) {
  return getMapData(this, key).get(key);
}

/**
 * 检查Map Cache中是否存在指定键
 * @param {*} key - 要检查的键
 * @returns {boolean} 是否存在该键
 * @original function piB(A) { return kj(this, A).has(A); }
 */
function mapCacheHas(key) {
  return getMapData(this, key).has(key);
}

/**
 * 设置Map Cache中的键值对
 * @param {*} key - 要设置的键
 * @param {*} value - 要设置的值
 * @returns {Object} Map Cache实例
 * @original function iiB(A, B) { var Q = kj(this, A), D = Q.size; return Q.set(A, B), this.size += Q.size == D ? 0 : 1, this; }
 */
function mapCacheSet(key, value) {
  const data = getMapData(this, key);
  const size = data.size;
  
  data.set(key, value);
  this.size += data.size == size ? 0 : 1;
  return this;
}

// 设置MapCache原型方法
MapCache.prototype.clear = mapCacheClear;
MapCache.prototype.delete = mapCacheDelete;
MapCache.prototype.get = mapCacheGet;
MapCache.prototype.has = mapCacheHas;
MapCache.prototype.set = mapCacheSet;

/**
 * 创建Map Cache实例
 * @param {Array} entries - 初始键值对数组
 * @returns {MapCache} Map Cache实例
 */
export function createMapCache(entries) {
  return new MapCache(entries);
}

/**
 * 检查值是否为Map Cache实例
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为Map Cache实例
 */
export function isMapCache(value) {
  return value instanceof MapCache;
}

/**
 * Map Cache工具函数
 */
export const MapCacheUtils = {
  /**
   * 将对象转换为Map Cache
   * @param {Object} object - 要转换的对象
   * @returns {MapCache} Map Cache实例
   */
  fromObject(object) {
    const entries = [];
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        entries.push([key, object[key]]);
      }
    }
    return new MapCache(entries);
  },

  /**
   * 将Map Cache转换为普通对象
   * @param {MapCache} mapCache - Map Cache实例
   * @returns {Object} 普通对象
   */
  toObject(mapCache) {
    if (!isMapCache(mapCache)) {
      throw new TypeError('Expected a MapCache instance');
    }
    
    const result = {};
    
    // 遍历所有存储
    const data = mapCache.__data__;
    
    // 处理hash存储
    if (data.hash && data.hash.size > 0) {
      const hashEntries = data.hash.__data__;
      for (const key in hashEntries) {
        if (Object.prototype.hasOwnProperty.call(hashEntries, key)) {
          const value = hashEntries[key];
          result[key] = value === "__lodash_hash_undefined__" ? undefined : value;
        }
      }
    }
    
    // 处理string存储
    if (data.string && data.string.size > 0) {
      const stringEntries = data.string.__data__;
      for (const key in stringEntries) {
        if (Object.prototype.hasOwnProperty.call(stringEntries, key)) {
          const value = stringEntries[key];
          result[key] = value === "__lodash_hash_undefined__" ? undefined : value;
        }
      }
    }
    
    // 处理map存储
    if (data.map && data.map.size > 0) {
      if (data.map instanceof Map) {
        data.map.forEach((value, key) => {
          result[key] = value;
        });
      } else if (data.map.__data__) {
        // ListCache
        const mapEntries = data.map.__data__;
        for (let i = 0; i < mapEntries.length; i++) {
          const [key, value] = mapEntries[i];
          result[key] = value;
        }
      }
    }
    
    return result;
  },

  /**
   * 获取Map Cache的所有键
   * @param {MapCache} mapCache - Map Cache实例
   * @returns {Array} 键数组
   */
  keys(mapCache) {
    if (!isMapCache(mapCache)) {
      throw new TypeError('Expected a MapCache instance');
    }
    
    const keys = [];
    const data = mapCache.__data__;
    
    // 收集hash存储的键
    if (data.hash && data.hash.size > 0) {
      const hashEntries = data.hash.__data__;
      for (const key in hashEntries) {
        if (Object.prototype.hasOwnProperty.call(hashEntries, key)) {
          keys.push(key);
        }
      }
    }
    
    // 收集string存储的键
    if (data.string && data.string.size > 0) {
      const stringEntries = data.string.__data__;
      for (const key in stringEntries) {
        if (Object.prototype.hasOwnProperty.call(stringEntries, key)) {
          keys.push(key);
        }
      }
    }
    
    // 收集map存储的键
    if (data.map && data.map.size > 0) {
      if (data.map instanceof Map) {
        data.map.forEach((value, key) => {
          keys.push(key);
        });
      } else if (data.map.__data__) {
        // ListCache
        const mapEntries = data.map.__data__;
        for (let i = 0; i < mapEntries.length; i++) {
          keys.push(mapEntries[i][0]);
        }
      }
    }
    
    return keys;
  },

  /**
   * 获取Map Cache的所有值
   * @param {MapCache} mapCache - Map Cache实例
   * @returns {Array} 值数组
   */
  values(mapCache) {
    if (!isMapCache(mapCache)) {
      throw new TypeError('Expected a MapCache instance');
    }
    
    const values = [];
    const data = mapCache.__data__;
    
    // 收集hash存储的值
    if (data.hash && data.hash.size > 0) {
      const hashEntries = data.hash.__data__;
      for (const key in hashEntries) {
        if (Object.prototype.hasOwnProperty.call(hashEntries, key)) {
          const value = hashEntries[key];
          values.push(value === "__lodash_hash_undefined__" ? undefined : value);
        }
      }
    }
    
    // 收集string存储的值
    if (data.string && data.string.size > 0) {
      const stringEntries = data.string.__data__;
      for (const key in stringEntries) {
        if (Object.prototype.hasOwnProperty.call(stringEntries, key)) {
          const value = stringEntries[key];
          values.push(value === "__lodash_hash_undefined__" ? undefined : value);
        }
      }
    }
    
    // 收集map存储的值
    if (data.map && data.map.size > 0) {
      if (data.map instanceof Map) {
        data.map.forEach((value, key) => {
          values.push(value);
        });
      } else if (data.map.__data__) {
        // ListCache
        const mapEntries = data.map.__data__;
        for (let i = 0; i < mapEntries.length; i++) {
          values.push(mapEntries[i][1]);
        }
      }
    }
    
    return values;
  }
};

// 导出MapCache构造函数作为默认导出
export default MapCache;
