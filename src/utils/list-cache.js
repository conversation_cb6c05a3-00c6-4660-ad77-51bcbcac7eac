/**
 * List Cache实现
 * @description 重构自原始文件中的List Cache实现，对应第1447-1489行
 * @original 原始代码行 1447-1489
 */

/**
 * 数组原型引用
 */
const arrayProto = Array.prototype;
const splice = arrayProto.splice;

/**
 * 在键值对数组中查找指定键的索引
 * @param {Array} data - 键值对数组
 * @param {*} key - 要查找的键
 * @returns {number} 找到的索引，未找到返回-1
 * @original function kiB(A, B) { var Q = A.length; while (Q--) if (mq(A[Q][0], B)) return Q; return -1; }
 */
function assocIndexOf(data, key) {
  let length = data.length;
  while (length--) {
    // @todo: 实现mq函数的逻辑（可能是严格相等比较）
    if (isEqual(data[length][0], key)) {
      return length;
    }
  }
  return -1;
}

/**
 * 简单的相等性比较函数
 * @param {*} value - 第一个值
 * @param {*} other - 第二个值
 * @returns {boolean} 是否相等
 * @original mq函数的实现
 */
function isEqual(value, other) {
  return value === other || (value !== value && other !== other);
}

/**
 * List Cache构造函数
 * @param {Array} entries - 初始键值对数组
 * @original function bc(A) { ... } (需要从上下文推断)
 */
export function ListCache(entries) {
  let index = -1;
  const length = entries == null ? 0 : entries.length;
  
  this.clear();
  while (++index < length) {
    const entry = entries[index];
    this.set(entry[0], entry[1]);
  }
}

/**
 * 清空List Cache
 * @original function yiB() { this.__data__ = [], this.size = 0; }
 */
function listCacheClear() {
  this.__data__ = [];
  this.size = 0;
}

/**
 * 删除List Cache中的键值对
 * @param {*} key - 要删除的键
 * @returns {boolean} 是否删除成功
 * @original function viB(A) { var B = this.__data__, Q = Sj(B, A); if (Q < 0) return !1; var D = B.length - 1; if (Q == D) B.pop();else xiB.call(B, Q, 1); return --this.size, !0; }
 */
function listCacheDelete(key) {
  const data = this.__data__;
  const index = assocIndexOf(data, key);
  
  if (index < 0) {
    return false;
  }
  
  const lastIndex = data.length - 1;
  if (index == lastIndex) {
    data.pop();
  } else {
    splice.call(data, index, 1);
  }
  
  --this.size;
  return true;
}

/**
 * 获取List Cache中指定键的值
 * @param {*} key - 要获取的键
 * @returns {*} 键对应的值
 * @original function biB(A) { var B = this.__data__, Q = Sj(B, A); return Q < 0 ? void 0 : B[Q][1]; }
 */
function listCacheGet(key) {
  const data = this.__data__;
  const index = assocIndexOf(data, key);
  return index < 0 ? undefined : data[index][1];
}

/**
 * 检查List Cache中是否存在指定键
 * @param {*} key - 要检查的键
 * @returns {boolean} 是否存在该键
 * @original function fiB(A) { return Sj(this.__data__, A) > -1; }
 */
function listCacheHas(key) {
  return assocIndexOf(this.__data__, key) > -1;
}

/**
 * 设置List Cache中的键值对
 * @param {*} key - 要设置的键
 * @param {*} value - 要设置的值
 * @returns {Object} List Cache实例
 * @original function hiB(A, B) { var Q = this.__data__, D = Sj(Q, A); if (D < 0) ++this.size, Q.push([A, B]);else Q[D][1] = B; return this; }
 */
function listCacheSet(key, value) {
  const data = this.__data__;
  const index = assocIndexOf(data, key);
  
  if (index < 0) {
    ++this.size;
    data.push([key, value]);
  } else {
    data[index][1] = value;
  }
  
  return this;
}

// 设置ListCache原型方法
ListCache.prototype.clear = listCacheClear;
ListCache.prototype.delete = listCacheDelete;
ListCache.prototype.get = listCacheGet;
ListCache.prototype.has = listCacheHas;
ListCache.prototype.set = listCacheSet;

/**
 * 创建List Cache实例
 * @param {Array} entries - 初始键值对数组
 * @returns {ListCache} List Cache实例
 */
export function createListCache(entries) {
  return new ListCache(entries);
}

/**
 * 检查值是否为List Cache实例
 * @param {*} value - 要检查的值
 * @returns {boolean} 是否为List Cache实例
 */
export function isListCache(value) {
  return value instanceof ListCache;
}

/**
 * List Cache工具函数
 */
export const ListCacheUtils = {
  /**
   * 将对象转换为List Cache
   * @param {Object} object - 要转换的对象
   * @returns {ListCache} List Cache实例
   */
  fromObject(object) {
    const entries = [];
    for (const key in object) {
      if (Object.prototype.hasOwnProperty.call(object, key)) {
        entries.push([key, object[key]]);
      }
    }
    return new ListCache(entries);
  },

  /**
   * 将List Cache转换为普通对象
   * @param {ListCache} listCache - List Cache实例
   * @returns {Object} 普通对象
   */
  toObject(listCache) {
    if (!isListCache(listCache)) {
      throw new TypeError('Expected a ListCache instance');
    }
    
    const result = {};
    const data = listCache.__data__;
    
    for (let i = 0; i < data.length; i++) {
      const [key, value] = data[i];
      result[key] = value;
    }
    
    return result;
  },

  /**
   * 获取List Cache的所有键
   * @param {ListCache} listCache - List Cache实例
   * @returns {Array} 键数组
   */
  keys(listCache) {
    if (!isListCache(listCache)) {
      throw new TypeError('Expected a ListCache instance');
    }
    
    const keys = [];
    const data = listCache.__data__;
    
    for (let i = 0; i < data.length; i++) {
      keys.push(data[i][0]);
    }
    
    return keys;
  },

  /**
   * 获取List Cache的所有值
   * @param {ListCache} listCache - List Cache实例
   * @returns {Array} 值数组
   */
  values(listCache) {
    if (!isListCache(listCache)) {
      throw new TypeError('Expected a ListCache instance');
    }
    
    const values = [];
    const data = listCache.__data__;
    
    for (let i = 0; i < data.length; i++) {
      values.push(data[i][1]);
    }
    
    return values;
  },

  /**
   * 获取List Cache的所有键值对
   * @param {ListCache} listCache - List Cache实例
   * @returns {Array} 键值对数组
   */
  entries(listCache) {
    if (!isListCache(listCache)) {
      throw new TypeError('Expected a ListCache instance');
    }
    
    return [...listCache.__data__];
  },

  /**
   * 遍历List Cache
   * @param {ListCache} listCache - List Cache实例
   * @param {Function} iteratee - 遍历函数
   */
  forEach(listCache, iteratee) {
    if (!isListCache(listCache)) {
      throw new TypeError('Expected a ListCache instance');
    }
    
    const data = listCache.__data__;
    
    for (let i = 0; i < data.length; i++) {
      const [key, value] = data[i];
      if (iteratee(value, key, listCache) === false) {
        break;
      }
    }
  },

  /**
   * 克隆List Cache
   * @param {ListCache} listCache - 要克隆的List Cache实例
   * @returns {ListCache} 克隆的List Cache实例
   */
  clone(listCache) {
    if (!isListCache(listCache)) {
      throw new TypeError('Expected a ListCache instance');
    }
    
    return new ListCache(this.entries(listCache));
  }
};

// 导出ListCache构造函数作为默认导出
export default ListCache;
