/**
 * 进程信号处理模块
 * @description 处理进程信号和清理工作
 */

/**
 * 设置进程信号处理器
 * @description 处理进程退出和中断信号
 * @original process.on("exit", () => { UR8(); }), process.on("SIGINT", () => { process.exit(0); });
 */
export function setupProcessHandlers() {
  // 进程退出时清理终端状态
  process.on("exit", () => {
    restoreTerminalCursor();
    performCleanup();
  });

  // 处理Ctrl+C中断信号
  process.on("SIGINT", () => {
    console.log('\nReceived SIGINT. Cleaning up...');
    performCleanup();
    process.exit(0);
  });

  // 处理终止信号
  process.on("SIGTERM", () => {
    console.log('Received SIGTERM. Cleaning up...');
    performCleanup();
    process.exit(0);
  });

  // 处理未捕获的异常
  process.on("uncaughtException", (error) => {
    console.error('Uncaught Exception:', error);
    performCleanup();
    process.exit(1);
  });

  // 处理未处理的Promise拒绝
  process.on("unhandledRejection", (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    performCleanup();
    process.exit(1);
  });
}

/**
 * 恢复终端光标
 * @description 恢复终端光标显示
 * @original function UR8() { (process.stderr.isTTY ? process.stderr : process.stdout.isTTY ? process.stdout : void 0)?.write(`\x1B[?25h${ZG0}`); }
 */
function restoreTerminalCursor() {
  const output = process.stderr.isTTY ? process.stderr : 
                 process.stdout.isTTY ? process.stdout : 
                 undefined;
  
  if (output) {
    // 恢复光标显示
    output.write('\x1B[?25h');
    
    // 重置终端状态
    resetTerminalState(output);
  }
}

/**
 * 重置终端状态
 * @param {NodeJS.WriteStream} output - 输出流
 * @original ${ZG0}
 */
function resetTerminalState(output) {
  // @todo: 实现ZG0的逻辑 (可能是终端重置序列)
  // 常见的终端重置序列
  output.write('\x1B[0m');     // 重置所有属性
  output.write('\x1B[?1049l'); // 退出备用屏幕缓冲区
  output.write('\x1B[?1000l'); // 禁用鼠标跟踪
}

/**
 * 执行清理工作
 */
function performCleanup() {
  try {
    // 清理临时文件
    cleanupTempFiles();
    
    // 关闭活跃连接
    closeActiveConnections();
    
    // 保存状态
    saveApplicationState();
    
    // 清理缓存
    clearCaches();
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
}

/**
 * 清理临时文件
 */
function cleanupTempFiles() {
  try {
    // @todo: 实现临时文件清理逻辑
    // 可以使用全局临时文件管理器
    const { getGlobalTempFileManager } = require('../utils/temp-file.js');
    const tempManager = getGlobalTempFileManager();
    const deletedCount = tempManager.cleanup();
    
    if (deletedCount > 0) {
      console.log(`Cleaned up ${deletedCount} temporary files`);
    }
  } catch (error) {
    console.error('Error cleaning up temp files:', error);
  }
}

/**
 * 关闭活跃连接
 */
function closeActiveConnections() {
  try {
    // @todo: 实现连接关闭逻辑
    // 关闭MCP连接、WebSocket连接等
    console.log('Closing active connections...');
  } catch (error) {
    console.error('Error closing connections:', error);
  }
}

/**
 * 保存应用程序状态
 */
function saveApplicationState() {
  try {
    // @todo: 实现状态保存逻辑
    // 保存会话状态、配置更改等
    console.log('Saving application state...');
  } catch (error) {
    console.error('Error saving application state:', error);
  }
}

/**
 * 清理缓存
 */
function clearCaches() {
  try {
    // @todo: 实现缓存清理逻辑
    // 清理内存缓存、文件缓存等
    console.log('Clearing caches...');
  } catch (error) {
    console.error('Error clearing caches:', error);
  }
}

/**
 * 设置优雅关闭处理器
 * @param {Function} cleanupCallback - 清理回调函数
 */
export function setupGracefulShutdown(cleanupCallback) {
  const shutdown = (signal) => {
    console.log(`Received ${signal}. Starting graceful shutdown...`);
    
    if (cleanupCallback && typeof cleanupCallback === 'function') {
      try {
        cleanupCallback();
      } catch (error) {
        console.error('Error in cleanup callback:', error);
      }
    }
    
    performCleanup();
    process.exit(0);
  };

  process.on('SIGINT', () => shutdown('SIGINT'));
  process.on('SIGTERM', () => shutdown('SIGTERM'));
}

/**
 * 检查进程是否在TTY环境中运行
 * @returns {boolean} 是否在TTY环境中
 */
export function isRunningInTTY() {
  return process.stdout.isTTY && process.stdin.isTTY;
}

/**
 * 检查进程是否在CI环境中运行
 * @returns {boolean} 是否在CI环境中
 */
export function isRunningInCI() {
  return !!(
    process.env.CI ||
    process.env.CONTINUOUS_INTEGRATION ||
    process.env.GITHUB_ACTIONS ||
    process.env.TRAVIS ||
    process.env.CIRCLECI ||
    process.env.JENKINS_URL
  );
}

/**
 * 获取进程信息
 * @returns {Object} 进程信息
 */
export function getProcessInfo() {
  return {
    pid: process.pid,
    ppid: process.ppid,
    platform: process.platform,
    arch: process.arch,
    nodeVersion: process.version,
    uptime: process.uptime(),
    memoryUsage: process.memoryUsage(),
    cpuUsage: process.cpuUsage(),
    cwd: process.cwd(),
    execPath: process.execPath,
    argv: process.argv,
    env: {
      NODE_ENV: process.env.NODE_ENV,
      CLAUDE_CODE_ENTRYPOINT: process.env.CLAUDE_CODE_ENTRYPOINT,
      CLAUDE_RUNTIME_TYPE: process.env.CLAUDE_RUNTIME_TYPE
    },
    isTTY: isRunningInTTY(),
    isCI: isRunningInCI()
  };
}

/**
 * 监控进程资源使用情况
 * @param {number} interval - 监控间隔（毫秒）
 * @param {Function} callback - 回调函数
 * @returns {NodeJS.Timeout} 定时器ID
 */
export function monitorProcessResources(interval = 5000, callback) {
  return setInterval(() => {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    const resourceInfo = {
      timestamp: Date.now(),
      memory: {
        rss: memUsage.rss,
        heapTotal: memUsage.heapTotal,
        heapUsed: memUsage.heapUsed,
        external: memUsage.external,
        arrayBuffers: memUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },
      uptime: process.uptime()
    };
    
    if (callback && typeof callback === 'function') {
      callback(resourceInfo);
    }
  }, interval);
}

/**
 * 设置内存使用警告
 * @param {number} threshold - 内存阈值（字节）
 * @param {Function} warningCallback - 警告回调函数
 */
export function setupMemoryWarning(threshold = 1024 * 1024 * 1024, warningCallback) {
  const checkMemory = () => {
    const memUsage = process.memoryUsage();
    if (memUsage.heapUsed > threshold) {
      const message = `High memory usage detected: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`;
      
      if (warningCallback && typeof warningCallback === 'function') {
        warningCallback(message, memUsage);
      } else {
        console.warn(message);
      }
    }
  };

  // 每30秒检查一次内存使用情况
  return setInterval(checkMemory, 30000);
}
