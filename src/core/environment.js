/**
 * 环境配置模块
 * @description 处理环境变量和运行时配置
 */

/**
 * 初始化环境配置
 * @param {boolean} isPrintMode - 是否为打印模式
 * @description 根据运行模式初始化环境
 * @original nj0(Q), sj0(!Q);
 */
export function initializeEnvironment(isPrintMode) {
  // 设置打印模式配置
  setPrintModeConfig(isPrintMode);
  
  // 设置交互模式配置
  setInteractiveModeConfig(!isPrintMode);
  
  // 设置其他环境配置
  setupEnvironmentVariables();
}

/**
 * 设置打印模式配置
 * @param {boolean} isPrintMode - 是否为打印模式
 * @original nj0(Q)
 */
function setPrintModeConfig(isPrintMode) {
  // @todo: 实现nj0()函数的逻辑
  if (isPrintMode) {
    // 打印模式下的配置
    process.env.CLAUDE_PRINT_MODE = 'true';
    
    // 禁用交互式功能
    process.env.CLAUDE_INTERACTIVE = 'false';
  }
}

/**
 * 设置交互模式配置
 * @param {boolean} isInteractive - 是否为交互模式
 * @original sj0(!Q)
 */
function setInteractiveModeConfig(isInteractive) {
  // @todo: 实现sj0()函数的逻辑
  if (isInteractive) {
    // 交互模式下的配置
    process.env.CLAUDE_INTERACTIVE = 'true';
    process.env.CLAUDE_PRINT_MODE = 'false';
    
    // 启用终端功能
    setupTerminalFeatures();
  }
}

/**
 * 设置环境变量
 */
function setupEnvironmentVariables() {
  // 确保必要的环境变量存在
  if (!process.env.CLAUDE_CODE_ENTRYPOINT) {
    process.env.CLAUDE_CODE_ENTRYPOINT = 'cli';
  }
  
  // 设置默认配置
  if (!process.env.CLAUDE_CONFIG_DIR) {
    process.env.CLAUDE_CONFIG_DIR = getDefaultConfigDirectory();
  }
  
  // 设置日志级别
  if (!process.env.CLAUDE_LOG_LEVEL) {
    process.env.CLAUDE_LOG_LEVEL = 'info';
  }
}

/**
 * 设置终端功能
 */
function setupTerminalFeatures() {
  // 启用颜色输出
  if (process.stdout.isTTY) {
    process.env.FORCE_COLOR = '1';
  }
  
  // 设置终端编码
  if (process.platform === 'win32') {
    process.env.PYTHONIOENCODING = 'utf-8';
  }
}

/**
 * 获取默认配置目录
 * @returns {string} 默认配置目录路径
 */
function getDefaultConfigDirectory() {
  const os = require('os');
  const path = require('path');
  
  switch (process.platform) {
    case 'win32':
      return path.join(os.homedir(), 'AppData', 'Roaming', 'claude-code');
    case 'darwin':
      return path.join(os.homedir(), 'Library', 'Application Support', 'claude-code');
    default:
      return path.join(os.homedir(), '.config', 'claude-code');
  }
}

/**
 * 检测运行时环境类型
 * @returns {string} 运行时环境类型
 * @original let Z = (() => { if (process.env.GITHUB_ACTIONS === "true") return "github-action"; ... })();
 */
export function detectRuntime() {
  if (process.env.GITHUB_ACTIONS === "true") {
    return "github-action";
  }
  
  if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-ts") {
    return "sdk-typescript";
  }
  
  if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-py") {
    return "sdk-python";
  }
  
  if (process.env.CLAUDE_CODE_ENTRYPOINT === "sdk-cli") {
    return "sdk-cli";
  }
  
  return "cli";
}

/**
 * 设置运行时类型
 * @param {string} runtimeType - 运行时类型
 * @description 配置运行时环境
 * @original oj0(Z);
 */
export function setRuntimeType(runtimeType) {
  // @todo: 实现oj0()函数的逻辑
  process.env.CLAUDE_RUNTIME_TYPE = runtimeType;
  
  // 根据运行时类型设置特定配置
  switch (runtimeType) {
    case 'github-action':
      setupGitHubActionEnvironment();
      break;
    case 'sdk-typescript':
    case 'sdk-python':
    case 'sdk-cli':
      setupSDKEnvironment(runtimeType);
      break;
    default:
      setupCLIEnvironment();
      break;
  }
}

/**
 * 设置GitHub Action环境
 */
function setupGitHubActionEnvironment() {
  // GitHub Actions特定配置
  process.env.CLAUDE_CI_MODE = 'true';
  process.env.CLAUDE_NO_INTERACTIVE = 'true';
  
  // 禁用颜色输出（除非明确启用）
  if (!process.env.FORCE_COLOR) {
    process.env.NO_COLOR = '1';
  }
}

/**
 * 设置SDK环境
 * @param {string} sdkType - SDK类型
 */
function setupSDKEnvironment(sdkType) {
  process.env.CLAUDE_SDK_MODE = 'true';
  process.env.CLAUDE_SDK_TYPE = sdkType.replace('sdk-', '');
  
  // SDK模式下通常不需要交互
  process.env.CLAUDE_NO_INTERACTIVE = 'true';
}

/**
 * 设置CLI环境
 */
function setupCLIEnvironment() {
  process.env.CLAUDE_CLI_MODE = 'true';
  
  // CLI模式下启用所有功能
  process.env.CLAUDE_FULL_FEATURES = 'true';
}

/**
 * 获取环境信息
 * @returns {Object} 环境信息对象
 */
export function getEnvironmentInfo() {
  return {
    platform: process.platform,
    arch: process.arch,
    nodeVersion: process.version,
    runtimeType: process.env.CLAUDE_RUNTIME_TYPE || 'unknown',
    entrypoint: process.env.CLAUDE_CODE_ENTRYPOINT || 'unknown',
    isPrintMode: process.env.CLAUDE_PRINT_MODE === 'true',
    isInteractive: process.env.CLAUDE_INTERACTIVE === 'true',
    isCIMode: process.env.CLAUDE_CI_MODE === 'true',
    isSDKMode: process.env.CLAUDE_SDK_MODE === 'true',
    configDir: process.env.CLAUDE_CONFIG_DIR,
    logLevel: process.env.CLAUDE_LOG_LEVEL || 'info'
  };
}

/**
 * 验证环境配置
 * @returns {Object} 验证结果
 */
export function validateEnvironment() {
  const issues = [];
  const warnings = [];
  
  // 检查Node.js版本
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 16) {
    issues.push(`Node.js version ${nodeVersion} is not supported. Please use Node.js 16 or later.`);
  } else if (majorVersion < 18) {
    warnings.push(`Node.js version ${nodeVersion} is supported but Node.js 18+ is recommended.`);
  }
  
  // 检查必要的环境变量
  const requiredEnvVars = ['CLAUDE_CODE_ENTRYPOINT'];
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      issues.push(`Missing required environment variable: ${envVar}`);
    }
  }
  
  // 检查配置目录
  const configDir = process.env.CLAUDE_CONFIG_DIR;
  if (configDir) {
    const fs = require('fs');
    try {
      if (!fs.existsSync(configDir)) {
        warnings.push(`Configuration directory does not exist: ${configDir}`);
      }
    } catch (error) {
      warnings.push(`Cannot access configuration directory: ${configDir}`);
    }
  }
  
  return {
    valid: issues.length === 0,
    issues,
    warnings
  };
}
