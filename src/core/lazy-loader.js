/**
 * 延迟加载器
 * @description 提供延迟初始化和缓存功能
 * @original 原始代码行 153
 */

/**
 * 创建延迟加载器
 * @description 创建一个延迟执行的函数，只在第一次调用时执行初始化函数
 * @param {Function} initializer - 初始化函数
 * @param {*} cachedResult - 缓存的结果
 * @returns {Function} 延迟加载函数
 * @original var gA1 = (A, B) => () => (A && (B = A(A = 0)), B);
 */
export function createLazyLoader(initializer, cachedResult) {
  return () => {
    if (initializer) {
      cachedResult = initializer();
      initializer = null; // 清除引用，避免重复执行和内存泄漏
    }
    return cachedResult;
  };
}

/**
 * 创建单次执行函数
 * @description 确保函数只执行一次，后续调用返回缓存结果
 * @param {Function} fn - 要执行的函数
 * @returns {Function} 单次执行函数
 */
export function once(fn) {
  let called = false;
  let result;
  
  return function(...args) {
    if (!called) {
      called = true;
      result = fn.apply(this, args);
    }
    return result;
  };
}

/**
 * 创建记忆化函数
 * @description 缓存函数执行结果，相同参数返回缓存值
 * @param {Function} fn - 要记忆化的函数
 * @param {Function} keyGenerator - 键生成函数，默认使用第一个参数
 * @returns {Function} 记忆化函数
 */
export function memoize(fn, keyGenerator = (arg) => arg) {
  const cache = new Map();
  
  return function(...args) {
    const key = keyGenerator.apply(this, args);
    
    if (cache.has(key)) {
      return cache.get(key);
    }
    
    const result = fn.apply(this, args);
    cache.set(key, result);
    return result;
  };
}
