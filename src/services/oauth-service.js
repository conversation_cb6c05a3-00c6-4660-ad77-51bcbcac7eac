/**
 * OAuth认证服务
 * @description 重构自原始文件中的OAuth认证代码，对应第5191-5300行
 * @original 原始代码行 5191-5300
 */

/**
 * 获取用户配置文件
 * @param {string} apiKey - API密钥
 * @returns {Promise<Object>} 用户配置文件
 * @original QWA函数
 */
export async function getUserProfile(apiKey) {
  const userConfig = getUserConfig();
  const accountUuid = userConfig.oauthAccount?.accountUuid;
  const apiKeyValue = getAPIKey(apiKey);
  
  if (!accountUuid || !apiKeyValue) {
    return;
  }

  const url = `${getAPIConfig().BASE_API_URL}/api/claude_cli_profile`;
  
  try {
    const response = await makeHTTPRequest('get', url, {
      headers: {
        "x-api-key": apiKeyValue,
        "anthropic-beta": getAnthropicBeta()
      },
      params: {
        account_uuid: accountUuid
      }
    });
    
    return response.data;
  } catch (error) {
    logError(error);
  }
}

/**
 * 获取OAuth配置文件
 * @param {string} accessToken - 访问令牌
 * @returns {Promise<Object>} OAuth配置文件
 * @original i91函数
 */
export async function getOAuthProfile(accessToken) {
  const url = `${getAPIConfig().BASE_API_URL}/api/oauth/profile`;
  
  try {
    const response = await makeHTTPRequest('get', url, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        "Content-Type": "application/json"
      }
    });
    
    return response.data;
  } catch (error) {
    logError(error);
  }
}

/**
 * 解析作用域字符串
 * @param {string} scopeString - 作用域字符串
 * @returns {Array} 作用域数组
 * @original iK1函数
 */
export function parseScopes(scopeString) {
  return scopeString?.split(" ").filter(Boolean) ?? [];
}

/**
 * 构建授权URL
 * @param {Object} options - 授权选项
 * @returns {string} 授权URL
 * @original or1函数
 */
export function buildAuthorizationURL({
  codeChallenge,
  state,
  isManual,
  loginWithClaudeAi,
  inferenceOnly
}) {
  const baseURL = loginWithClaudeAi ? 
    getAPIConfig().CLAUDE_AI_AUTHORIZE_URL : 
    getAPIConfig().CONSOLE_AUTHORIZE_URL;
  
  const url = new URL(baseURL);
  
  // 设置基本参数
  url.searchParams.append("code", "true");
  url.searchParams.append("client_id", getAPIConfig().CLIENT_ID);
  url.searchParams.append("response_type", "code");
  url.searchParams.append("redirect_uri", 
    isManual ? 
      getAPIConfig().MANUAL_REDIRECT_URL : 
      `http://localhost:${getAPIConfig().REDIRECT_PORT}/callback`
  );
  
  // 设置作用域
  const scopes = inferenceOnly ? [getInferenceScope()] : getAPIConfig().SCOPES;
  url.searchParams.append("scope", scopes.join(" "));
  
  // 设置PKCE参数
  url.searchParams.append("code_challenge", codeChallenge);
  url.searchParams.append("code_challenge_method", "S256");
  url.searchParams.append("state", state);
  
  return url.toString();
}

/**
 * 交换授权码获取令牌
 * @param {string} code - 授权码
 * @param {string} state - 状态参数
 * @param {string} codeVerifier - 代码验证器
 * @param {boolean} isManual - 是否手动模式
 * @param {number} expiresIn - 过期时间（可选）
 * @returns {Promise<Object>} 令牌响应
 * @original DWA函数
 */
export async function exchangeCodeForToken(code, state, codeVerifier, isManual = false, expiresIn) {
  const tokenData = {
    grant_type: "authorization_code",
    code,
    redirect_uri: isManual ? 
      getAPIConfig().MANUAL_REDIRECT_URL : 
      `http://localhost:${getAPIConfig().REDIRECT_PORT}/callback`,
    client_id: getAPIConfig().CLIENT_ID,
    code_verifier: codeVerifier,
    state
  };
  
  if (expiresIn !== undefined) {
    tokenData.expires_in = expiresIn;
  }
  
  const response = await makeHTTPRequest('post', getAPIConfig().TOKEN_URL, tokenData, {
    headers: {
      "Content-Type": "application/json"
    }
  });
  
  if (response.status !== 200) {
    throw new Error(
      response.status === 401 ? 
        "Authentication failed: Invalid authorization code" : 
        `Token exchange failed (${response.status}): ${response.statusText}`
    );
  }
  
  return response.data;
}

/**
 * 刷新访问令牌
 * @param {string} refreshToken - 刷新令牌
 * @returns {Promise<Object>} 刷新后的令牌信息
 * @original ZWA函数
 */
export async function refreshAccessToken(refreshToken) {
  const tokenData = {
    grant_type: "refresh_token",
    refresh_token: refreshToken,
    client_id: getAPIConfig().CLIENT_ID
  };
  
  try {
    const response = await makeHTTPRequest('post', getAPIConfig().TOKEN_URL, tokenData, {
      headers: {
        "Content-Type": "application/json"
      }
    });
    
    if (response.status !== 200) {
      throw new Error(`Token refresh failed: ${response.statusText}`);
    }
    
    const data = response.data;
    const {
      access_token: accessToken,
      refresh_token: newRefreshToken = refreshToken,
      expires_in: expiresIn
    } = data;
    
    const expiresAt = Date.now() + expiresIn * 1000;
    const scopes = parseScopes(data.scope);
    
    // 记录成功事件
    recordEvent("tengu_oauth_token_refresh_success", {});
    
    // 获取订阅类型
    const subscriptionType = await getSubscriptionType(accessToken);
    
    return {
      accessToken,
      refreshToken: newRefreshToken,
      expiresAt,
      scopes,
      subscriptionType
    };
  } catch (error) {
    // 记录失败事件
    recordEvent("tengu_oauth_token_refresh_failure", {});
    throw error;
  }
}

/**
 * 获取用户角色
 * @param {string} accessToken - 访问令牌
 * @returns {Promise<Object>} 用户角色信息
 * @original GWA函数
 */
export async function getUserRoles(accessToken) {
  const response = await makeHTTPRequest('get', getAPIConfig().ROLES_URL, {
    headers: {
      Authorization: `Bearer ${accessToken}`
    }
  });
  
  if (response.status !== 200) {
    throw new Error(`Failed to fetch user roles: ${response.statusText}`);
  }
  
  const rolesData = response.data;
  const userConfig = getUserConfig();
  
  if (!userConfig.oauthAccount) {
    throw new Error("OAuth account information not found in config");
  }
  
  return rolesData;
}

/**
 * OAuth服务类
 * @description 提供完整的OAuth认证功能
 */
export class OAuthService {
  constructor() {
    this.tokenCache = new Map();
    this.profileCache = new Map();
    this.cacheTimeout = 300000; // 5分钟缓存
  }

  /**
   * 开始OAuth流程
   * @param {Object} options - OAuth选项
   * @returns {Object} 授权信息
   */
  startAuthFlow(options = {}) {
    const {
      isManual = false,
      loginWithClaudeAi = false,
      inferenceOnly = false
    } = options;
    
    // 生成PKCE参数
    const codeVerifier = this.generateCodeVerifier();
    const codeChallenge = this.generateCodeChallenge(codeVerifier);
    const state = this.generateState();
    
    const authURL = buildAuthorizationURL({
      codeChallenge,
      state,
      isManual,
      loginWithClaudeAi,
      inferenceOnly
    });
    
    return {
      authURL,
      codeVerifier,
      state
    };
  }

  /**
   * 完成OAuth流程
   * @param {string} code - 授权码
   * @param {string} state - 状态参数
   * @param {string} codeVerifier - 代码验证器
   * @param {Object} options - 选项
   * @returns {Promise<Object>} 令牌信息
   */
  async completeAuthFlow(code, state, codeVerifier, options = {}) {
    const { isManual = false, expiresIn } = options;
    
    const tokenData = await exchangeCodeForToken(code, state, codeVerifier, isManual, expiresIn);
    
    // 缓存令牌信息
    this.cacheTokenData(tokenData);
    
    return tokenData;
  }

  /**
   * 刷新令牌
   * @param {string} refreshToken - 刷新令牌
   * @returns {Promise<Object>} 新的令牌信息
   */
  async refreshToken(refreshToken) {
    const tokenInfo = await refreshAccessToken(refreshToken);
    
    // 更新缓存
    this.cacheTokenData(tokenInfo);
    
    return tokenInfo;
  }

  /**
   * 获取用户信息
   * @param {string} accessToken - 访问令牌
   * @param {boolean} useCache - 是否使用缓存
   * @returns {Promise<Object>} 用户信息
   */
  async getUserInfo(accessToken, useCache = true) {
    const cacheKey = `profile:${accessToken}`;
    
    if (useCache) {
      const cached = this.getFromCache(this.profileCache, cacheKey);
      if (cached) {
        return cached;
      }
    }
    
    const profile = await getOAuthProfile(accessToken);
    
    if (useCache && profile) {
      this.setCache(this.profileCache, cacheKey, profile);
    }
    
    return profile;
  }

  /**
   * 验证令牌是否有效
   * @param {string} accessToken - 访问令牌
   * @returns {Promise<boolean>} 是否有效
   */
  async validateToken(accessToken) {
    try {
      await this.getUserInfo(accessToken, false);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 生成代码验证器
   * @returns {string} 代码验证器
   * @private
   */
  generateCodeVerifier() {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('base64url');
  }

  /**
   * 生成代码挑战
   * @param {string} codeVerifier - 代码验证器
   * @returns {string} 代码挑战
   * @private
   */
  generateCodeChallenge(codeVerifier) {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(codeVerifier).digest('base64url');
  }

  /**
   * 生成状态参数
   * @returns {string} 状态参数
   * @private
   */
  generateState() {
    const crypto = require('crypto');
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * 缓存令牌数据
   * @param {Object} tokenData - 令牌数据
   * @private
   */
  cacheTokenData(tokenData) {
    const cacheKey = `token:${tokenData.accessToken}`;
    this.setCache(this.tokenCache, cacheKey, tokenData);
  }

  /**
   * 从缓存获取数据
   * @param {Map} cache - 缓存对象
   * @param {string} key - 缓存键
   * @returns {*} 缓存数据或null
   * @private
   */
  getFromCache(cache, key) {
    const cached = cache.get(key);
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  /**
   * 设置缓存
   * @param {Map} cache - 缓存对象
   * @param {string} key - 缓存键
   * @param {*} data - 数据
   * @private
   */
  setCache(cache, key, data) {
    cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.tokenCache.clear();
    this.profileCache.clear();
  }

  /**
   * 获取缓存统计
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      tokenCacheSize: this.tokenCache.size,
      profileCacheSize: this.profileCache.size,
      cacheTimeout: this.cacheTimeout
    };
  }
}

// 辅助函数

/**
 * 获取用户配置
 * @returns {Object} 用户配置
 * @original E0函数的实现（推测）
 */
function getUserConfig() {
  // @todo: 实现E0函数的实际逻辑
  return { oauthAccount: {} };
}

/**
 * 获取API密钥
 * @param {string} key - 密钥标识
 * @returns {string} API密钥
 * @original RY函数的实现（推测）
 */
function getAPIKey(key) {
  // @todo: 实现RY函数的实际逻辑
  return key;
}

/**
 * 获取API配置
 * @returns {Object} API配置
 * @original P5函数的实现（推测）
 */
function getAPIConfig() {
  return {
    BASE_API_URL: "https://api.anthropic.com",
    CLAUDE_AI_AUTHORIZE_URL: "https://claude.ai/oauth/authorize",
    CONSOLE_AUTHORIZE_URL: "https://console.anthropic.com/oauth/authorize",
    TOKEN_URL: "https://api.anthropic.com/oauth/token",
    ROLES_URL: "https://api.anthropic.com/oauth/roles",
    CLIENT_ID: "claude-cli",
    MANUAL_REDIRECT_URL: "urn:ietf:wg:oauth:2.0:oob",
    REDIRECT_PORT: 8080,
    SCOPES: ["read", "write"]
  };
}

/**
 * 获取Anthropic Beta版本
 * @returns {string} Beta版本
 * @original Vp变量的实现（推测）
 */
function getAnthropicBeta() {
  return "2023-06-01";
}

/**
 * 获取推理作用域
 * @returns {string} 推理作用域
 * @original hV1变量的实现（推测）
 */
function getInferenceScope() {
  return "inference";
}

/**
 * 发起HTTP请求
 * @param {string} method - HTTP方法
 * @param {string} url - 请求URL
 * @param {*} data - 请求数据
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 响应对象
 * @original v9对象的实现（推测）
 */
async function makeHTTPRequest(method, url, data, config = {}) {
  // @todo: 实现v9对象的实际逻辑
  const axios = require('axios');
  
  if (method === 'get') {
    return axios.get(url, { ...config, ...data });
  } else if (method === 'post') {
    return axios.post(url, data, config);
  }
  
  throw new Error(`Unsupported HTTP method: ${method}`);
}

/**
 * 获取订阅类型
 * @param {string} accessToken - 访问令牌
 * @returns {Promise<string>} 订阅类型
 * @original tr1函数的实现（推测）
 */
async function getSubscriptionType(accessToken) {
  // @todo: 实现tr1函数的实际逻辑
  return "free";
}

/**
 * 记录事件
 * @param {string} eventName - 事件名称
 * @param {Object} eventData - 事件数据
 * @original C1函数的实现（推测）
 */
function recordEvent(eventName, eventData) {
  // @todo: 实现C1函数的实际逻辑
  console.log(`Event: ${eventName}`, eventData);
}

/**
 * 记录错误
 * @param {Error} error - 错误对象
 * @original T1函数的实现（推测）
 */
function logError(error) {
  console.error('OAuth error:', error);
}

// 创建默认OAuth服务实例
export const oauthService = new OAuthService();
