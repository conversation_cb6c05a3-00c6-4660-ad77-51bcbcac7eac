/**
 * PDF处理服务
 * @description 重构自原始文件中的PDF处理代码，对应第2926-2949行
 * @original 原始代码行 2926-2949
 */

import { getFileSystem } from '../core/module-imports.js';

/**
 * 支持的PDF文件扩展名
 * @original o6Q变量
 */
const PDF_EXTENSIONS = new Set(["pdf"]);

/**
 * PDF文件最大大小（32MB）
 * @original WIA变量
 */
const MAX_PDF_SIZE = 33554432; // 32MB in bytes

/**
 * 检查是否为第一方环境
 * @returns {boolean} 是否为第一方环境
 * @original Vi函数
 */
function isFirstPartyEnvironment() {
  // @todo: 实现uD函数的逻辑
  return getEnvironmentType() === "firstParty";
}

/**
 * 获取环境类型
 * @returns {string} 环境类型
 * @original TN函数和uD函数
 */
function getEnvironmentType() {
  // @todo: 实现uD函数的实际逻辑
  return process.env.ENVIRONMENT_TYPE || "thirdParty";
}

/**
 * 检查文件扩展名是否为PDF
 * @param {string} extension - 文件扩展名
 * @returns {boolean} 是否为PDF文件
 * @original GK1函数
 */
export function isPDFExtension(extension) {
  const normalizedExtension = extension.startsWith(".") ? extension.slice(1) : extension;
  return PDF_EXTENSIONS.has(normalizedExtension.toLowerCase());
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 * @original wY函数的实现（推测）
 */
function formatFileSize(bytes) {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

/**
 * 处理PDF文件
 * @param {string} filePath - PDF文件路径
 * @returns {Promise<Object>} PDF处理结果
 * @throws {Error} 文件处理错误
 * @original JIA函数
 */
export async function processPDFFile(filePath) {
  const fs = getFileSystem();
  
  // 检查文件状态
  let stats;
  try {
    stats = fs.statSync(filePath);
  } catch (error) {
    throw new Error(`Cannot access PDF file: ${filePath} - ${error.message}`);
  }
  
  const fileSize = stats.size;
  
  // 检查文件是否为空
  if (fileSize === 0) {
    throw new Error(`PDF file is empty: ${filePath}`);
  }
  
  // 检查文件大小是否超过限制
  if (fileSize > MAX_PDF_SIZE) {
    throw new Error(
      `PDF file size (${formatFileSize(fileSize)}) exceeds maximum allowed size (${formatFileSize(MAX_PDF_SIZE)}). ` +
      `PDF files must be less than 32MB.`
    );
  }
  
  // 读取文件并转换为base64
  let fileBuffer;
  try {
    fileBuffer = fs.readFileBytesSync ? 
      fs.readFileBytesSync(filePath) : 
      fs.readFileSync(filePath);
  } catch (error) {
    throw new Error(`Failed to read PDF file: ${filePath} - ${error.message}`);
  }
  
  const base64Content = fileBuffer.toString("base64");
  
  return {
    type: "pdf",
    file: {
      filePath,
      base64: base64Content,
      originalSize: fileSize
    }
  };
}

/**
 * PDF服务类
 * @description 提供PDF文件处理的完整功能
 */
export class PDFService {
  constructor() {
    this.maxFileSize = MAX_PDF_SIZE;
    this.supportedExtensions = PDF_EXTENSIONS;
  }

  /**
   * 检查是否支持PDF处理
   * @returns {boolean} 是否支持PDF处理
   */
  isSupported() {
    return isFirstPartyEnvironment();
  }

  /**
   * 验证PDF文件
   * @param {string} filePath - 文件路径
   * @returns {Object} 验证结果
   */
  validatePDFFile(filePath) {
    const fs = getFileSystem();
    const result = {
      valid: false,
      errors: [],
      warnings: [],
      fileInfo: null
    };
    
    try {
      // 检查文件是否存在
      const stats = fs.statSync(filePath);
      
      result.fileInfo = {
        path: filePath,
        size: stats.size,
        modified: stats.mtime,
        created: stats.birthtime || stats.ctime
      };
      
      // 检查文件扩展名
      const extension = filePath.split('.').pop() || '';
      if (!this.isPDFFile(extension)) {
        result.errors.push(`File does not have a PDF extension: ${extension}`);
      }
      
      // 检查文件大小
      if (stats.size === 0) {
        result.errors.push('PDF file is empty');
      } else if (stats.size > this.maxFileSize) {
        result.errors.push(
          `File size (${formatFileSize(stats.size)}) exceeds maximum allowed size (${formatFileSize(this.maxFileSize)})`
        );
      }
      
      // 如果文件很大但在限制内，添加警告
      if (stats.size > this.maxFileSize * 0.8) {
        result.warnings.push(
          `File size (${formatFileSize(stats.size)}) is close to the maximum limit`
        );
      }
      
      result.valid = result.errors.length === 0;
      
    } catch (error) {
      result.errors.push(`Cannot access file: ${error.message}`);
    }
    
    return result;
  }

  /**
   * 检查文件是否为PDF
   * @param {string} extension - 文件扩展名
   * @returns {boolean} 是否为PDF文件
   */
  isPDFFile(extension) {
    return isPDFExtension(extension);
  }

  /**
   * 异步处理PDF文件
   * @param {string} filePath - PDF文件路径
   * @returns {Promise<Object>} 处理结果
   */
  async processFile(filePath) {
    // 首先验证文件
    const validation = this.validatePDFFile(filePath);
    if (!validation.valid) {
      throw new Error(`PDF validation failed: ${validation.errors.join(', ')}`);
    }
    
    // 处理文件
    return processPDFFile(filePath);
  }

  /**
   * 批量处理PDF文件
   * @param {string[]} filePaths - PDF文件路径数组
   * @returns {Promise<Object>} 批量处理结果
   */
  async processMultipleFiles(filePaths) {
    const results = {
      successful: [],
      failed: [],
      totalSize: 0
    };
    
    for (const filePath of filePaths) {
      try {
        const result = await this.processFile(filePath);
        results.successful.push(result);
        results.totalSize += result.file.originalSize;
      } catch (error) {
        results.failed.push({
          filePath,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 获取PDF文件信息
   * @param {string} filePath - PDF文件路径
   * @returns {Object} 文件信息
   */
  getFileInfo(filePath) {
    const validation = this.validatePDFFile(filePath);
    return {
      ...validation.fileInfo,
      valid: validation.valid,
      errors: validation.errors,
      warnings: validation.warnings,
      isPDF: this.isPDFFile(filePath.split('.').pop() || ''),
      supported: this.isSupported()
    };
  }

  /**
   * 设置最大文件大小
   * @param {number} maxSize - 最大文件大小（字节）
   */
  setMaxFileSize(maxSize) {
    this.maxFileSize = maxSize;
  }

  /**
   * 获取最大文件大小
   * @returns {number} 最大文件大小（字节）
   */
  getMaxFileSize() {
    return this.maxFileSize;
  }

  /**
   * 获取支持的文件扩展名
   * @returns {Set} 支持的扩展名集合
   */
  getSupportedExtensions() {
    return new Set(this.supportedExtensions);
  }

  /**
   * 获取服务统计信息
   * @returns {Object} 服务统计信息
   */
  getStats() {
    return {
      maxFileSize: this.maxFileSize,
      maxFileSizeFormatted: formatFileSize(this.maxFileSize),
      supportedExtensions: Array.from(this.supportedExtensions),
      isSupported: this.isSupported(),
      environment: getEnvironmentType()
    };
  }
}

/**
 * PDF工具函数
 */
export const PDFUtils = {
  /**
   * 检查文件路径是否为PDF
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否为PDF文件
   */
  isPDFFile(filePath) {
    const extension = filePath.split('.').pop() || '';
    return isPDFExtension(extension);
  },

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize,

  /**
   * 获取PDF文件的MIME类型
   * @returns {string} MIME类型
   */
  getMimeType() {
    return 'application/pdf';
  },

  /**
   * 检查base64字符串是否为PDF
   * @param {string} base64String - base64字符串
   * @returns {boolean} 是否为PDF
   */
  isBase64PDF(base64String) {
    try {
      const buffer = Buffer.from(base64String, 'base64');
      const header = buffer.toString('ascii', 0, 4);
      return header === '%PDF';
    } catch {
      return false;
    }
  },

  /**
   * 获取PDF版本信息
   * @param {string} base64String - base64字符串
   * @returns {string|null} PDF版本或null
   */
  getPDFVersion(base64String) {
    try {
      const buffer = Buffer.from(base64String, 'base64');
      const header = buffer.toString('ascii', 0, 8);
      const versionMatch = header.match(/%PDF-(\d\.\d)/);
      return versionMatch ? versionMatch[1] : null;
    } catch {
      return null;
    }
  }
};

// 创建默认PDF服务实例
export const pdfService = new PDFService();
