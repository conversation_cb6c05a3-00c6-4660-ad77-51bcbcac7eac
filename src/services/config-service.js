/**
 * 配置管理服务
 * @description Claude Code的配置文件管理功能
 * @original 原始代码分布在多个位置，主要在3197行、4857行等
 */

import { join as joinPath } from 'path';
import { homedir } from 'os';

/**
 * 获取配置文件路径
 * @param {string} scope - 配置作用域 (local, user, project)
 * @returns {string} 配置文件路径
 * @original function sC(A) { let B = x1(); switch (A) { case "user": { let Q = NY(), D = B.existsSync(Q); return `${Q}${D ? "" : " (file does not exist)"}`; } case "project": { let Q = qYA(a0(), ".mcp.json"), D = B.existsSync(Q); return `${Q}${D ? "" : " (file does not exist)"}`; } case "local": { let Q = qYA(a0(), ".claude", "config.json"), D = B.existsSync(Q); return `${Q}${D ? "" : " (file does not exist)"}`; } default: throw new Error(`Unknown scope: ${A}`); } }
 */
export function getConfigPath(scope) {
  const fs = require('fs'); // @todo: 使用现代ES模块导入
  
  switch (scope) {
    case "user": {
      // 用户全局配置路径
      const userConfigPath = getUserConfigPath();
      const exists = fs.existsSync(userConfigPath);
      return `${userConfigPath}${exists ? "" : " (file does not exist)"}`;
    }
    
    case "project": {
      // 项目配置路径 (.mcp.json)
      const projectConfigPath = joinPath(process.cwd(), ".mcp.json");
      const exists = fs.existsSync(projectConfigPath);
      return `${projectConfigPath}${exists ? "" : " (file does not exist)"}`;
    }
    
    case "local": {
      // 本地配置路径 (.claude/config.json)
      const localConfigPath = joinPath(process.cwd(), ".claude", "config.json");
      const exists = fs.existsSync(localConfigPath);
      return `${localConfigPath}${exists ? "" : " (file does not exist)"}`;
    }
    
    default:
      throw new Error(`Unknown scope: ${scope}`);
  }
}

/**
 * 获取用户配置目录路径
 * @returns {string} 用户配置目录路径
 */
function getUserConfigPath() {
  // @todo: 实现NY()函数的逻辑
  return joinPath(homedir(), '.claude', 'config.json');
}

/**
 * 验证并添加MCP服务器配置
 * @param {string} serverName - 服务器名称
 * @param {Object} serverConfig - 服务器配置
 * @param {string} scope - 配置作用域
 * @throws {Error} 验证失败或服务器已存在时抛出错误
 * @original function Hh(A, B, Q) { if (A.match(/[^a-zA-Z0-9_-]/)) throw new Error(`Invalid name ${A}. Names can only contain letters, numbers, hyphens, and underscores.`); let D = Cr1.safeParse(B); if (!D.success) { let G = D.error.errors.map(F => `${F.path.join(".")}: ${F.message}`).join(", "); throw new Error(`Invalid configuration: ${G}`); } ... }
 */
export function validateAndAddMcpServer(serverName, serverConfig, scope) {
  // 验证服务器名称
  if (serverName.match(/[^a-zA-Z0-9_-]/)) {
    throw new Error(`Invalid name ${serverName}. Names can only contain letters, numbers, hyphens, and underscores.`);
  }
  
  // 验证配置格式
  const validationResult = validateMcpServerConfig(serverConfig);
  if (!validationResult.success) {
    const errorMessages = validationResult.errors.map(error => 
      `${error.path.join(".")}: ${error.message}`
    ).join(", ");
    throw new Error(`Invalid configuration: ${errorMessages}`);
  }
  
  // 检查服务器是否已存在
  switch (scope) {
    case "project": {
      const projectConfig = loadConfigByScope("project");
      if (projectConfig.servers[serverName]) {
        throw new Error(`MCP server ${serverName} already exists in .mcp.json`);
      }
      break;
    }
    
    case "user": {
      const userConfig = loadUserConfig();
      if (userConfig.mcpServers?.[serverName]) {
        throw new Error(`MCP server ${serverName} already exists in user config`);
      }
      break;
    }
    
    case "local": {
      const localConfig = loadLocalConfig();
      if (localConfig.mcpServers?.[serverName]) {
        throw new Error(`MCP server ${serverName} already exists in local config`);
      }
      break;
    }
    
    default:
      throw new Error(`Cannot add MCP server to scope: ${scope}`);
  }
  
  // 添加服务器配置
  addMcpServerToConfig(serverName, serverConfig, scope);
}

/**
 * 根据作用域加载配置
 * @param {string} scope - 配置作用域
 * @returns {Object} 配置对象，包含servers和errors属性
 * @original function pZ(scope) - 在多个地方被调用
 */
export function loadConfigByScope(scope) {
  try {
    switch (scope) {
      case "user":
        return loadUserConfigWithValidation();
      case "project":
        return loadProjectConfigWithValidation();
      case "local":
        return loadLocalConfigWithValidation();
      default:
        throw new Error(`Unknown config scope: ${scope}`);
    }
  } catch (error) {
    return {
      servers: {},
      errors: [{ message: error.message, path: [], severity: "fatal" }]
    };
  }
}

/**
 * 加载用户配置
 * @returns {Object} 用户配置对象
 */
function loadUserConfigWithValidation() {
  const fs = require('fs');
  const configPath = getUserConfigPath();
  
  if (!fs.existsSync(configPath)) {
    return { servers: {}, errors: [] };
  }
  
  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configContent);
    
    // 转换格式：将mcpServers转换为servers，并添加scope信息
    const servers = {};
    if (config.mcpServers) {
      for (const [name, serverConfig] of Object.entries(config.mcpServers)) {
        servers[name] = { ...serverConfig, scope: "user" };
      }
    }
    
    return { servers, errors: [] };
  } catch (error) {
    return {
      servers: {},
      errors: [{ message: `Failed to parse user config: ${error.message}`, path: [], severity: "fatal" }]
    };
  }
}

/**
 * 加载项目配置
 * @returns {Object} 项目配置对象
 */
function loadProjectConfigWithValidation() {
  const fs = require('fs');
  const configPath = joinPath(process.cwd(), ".mcp.json");
  
  if (!fs.existsSync(configPath)) {
    return { servers: {}, errors: [] };
  }
  
  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configContent);
    
    // 添加scope信息
    const servers = {};
    if (config.mcpServers) {
      for (const [name, serverConfig] of Object.entries(config.mcpServers)) {
        servers[name] = { ...serverConfig, scope: "project" };
      }
    }
    
    return { servers, errors: [] };
  } catch (error) {
    return {
      servers: {},
      errors: [{ message: `Failed to parse project config: ${error.message}`, path: [], severity: "fatal" }]
    };
  }
}

/**
 * 加载本地配置
 * @returns {Object} 本地配置对象
 */
function loadLocalConfigWithValidation() {
  const fs = require('fs');
  const configPath = joinPath(process.cwd(), ".claude", "config.json");
  
  if (!fs.existsSync(configPath)) {
    return { servers: {}, errors: [] };
  }
  
  try {
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configContent);
    
    // 添加scope信息
    const servers = {};
    if (config.mcpServers) {
      for (const [name, serverConfig] of Object.entries(config.mcpServers)) {
        servers[name] = { ...serverConfig, scope: "local" };
      }
    }
    
    return { servers, errors: [] };
  } catch (error) {
    return {
      servers: {},
      errors: [{ message: `Failed to parse local config: ${error.message}`, path: [], severity: "fatal" }]
    };
  }
}

/**
 * 获取所有已配置的MCP服务器
 * @returns {Object} 所有服务器配置的合并对象
 * @original function Qz() { let { servers: A } = pZ("user"), { servers: B } = pZ("project"), { servers: Q } = pZ("local"), D = {}; for (let [G, F] of Object.entries(B)) if (EK1(G) === "approved") D[G] = F; let Z = Object.assign({}, A, D, Q); return C1("tengu_mcp_servers", { global: Object.keys(A).length, project: Object.keys(D).length, user: Object.keys(Q).length }), Z; }
 */
export function getAllMcpServers() {
  const userConfig = loadConfigByScope("user");
  const projectConfig = loadConfigByScope("project");
  const localConfig = loadConfigByScope("local");
  
  // 过滤已批准的项目服务器
  const approvedProjectServers = {};
  for (const [name, serverConfig] of Object.entries(projectConfig.servers)) {
    if (getMcpServerApprovalStatus(name) === "approved") {
      approvedProjectServers[name] = serverConfig;
    }
  }
  
  // 合并配置，优先级：local > project > user
  const allServers = Object.assign(
    {},
    userConfig.servers,
    approvedProjectServers,
    localConfig.servers
  );
  
  // 记录统计信息
  logMcpServerStats({
    global: Object.keys(userConfig.servers).length,
    project: Object.keys(approvedProjectServers).length,
    user: Object.keys(localConfig.servers).length
  });
  
  return allServers;
}

// 这些函数需要在其他模块中实现
function validateMcpServerConfig(config) {
  // @todo: 实现Cr1.safeParse的逻辑
  return { success: true, errors: [] };
}

function loadUserConfig() {
  // @todo: 实现E0()函数的逻辑
  return { mcpServers: {} };
}

function loadLocalConfig() {
  // @todo: 实现t9()函数的逻辑
  return { mcpServers: {} };
}

function addMcpServerToConfig(serverName, serverConfig, scope) {
  // @todo: 实现配置写入逻辑
  throw new Error("addMcpServerToConfig not implemented");
}

function getMcpServerApprovalStatus(serverName) {
  // @todo: 实现EK1()函数的逻辑
  return "approved";
}

function logMcpServerStats(stats) {
  // @todo: 实现C1()函数的逻辑
  console.log("MCP server stats:", stats);
}
