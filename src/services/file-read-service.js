/**
 * 文件读取服务
 * @description 重构自原始文件中的文件读取工具代码，对应第2950-3000行
 * @original 原始代码行 2950-3000
 */

import { isFirstPartyEnvironment } from './pdf-service.js';

/**
 * 文件读取工具常量
 * @original 第2950-2968行的常量定义
 */
export const FILE_READ_TOOL = {
  name: "Read",
  maxLines: 2000,
  maxLineLength: 2000,
  description: "Read a file from the local filesystem.",
  longDescription: `Reads a file from the local filesystem. You can access any file directly by using this tool.
Assume this tool is able to read all files on the machine. If the User provides a path to a file assume that path is valid. It is okay to read a file that does not exist; an error will be returned.

Usage:
- The file_path parameter must be an absolute path, not a relative path
- By default, it reads up to 2000 lines starting from the beginning of the file
- You can optionally specify a line offset and limit (especially handy for long files), but it's recommended to read the whole file by not providing these parameters
- Any lines longer than 2000 characters will be truncated
- Results are returned using cat -n format, with line numbers starting at 1
- This tool allows Claude Code to read images (eg PNG, JPG, etc). When reading an image file the contents are presented visually as Claude Code is a multimodal LLM.${isFirstPartyEnvironment() ? `
- This tool can read PDF files (.pdf). PDFs are processed page by page, extracting both text and visual content for analysis.` : ""}
- This tool can read Jupyter notebooks (.ipynb files) and returns all cells with their outputs, combining code, text, and visualizations.
- You have the capability to call multiple tools in a single response. It is always better to speculatively read multiple files as a batch that are potentially useful. 
- You will regularly be asked to read screenshots. If the user provides a path to a screenshot ALWAYS use this tool to view the file at the path. This tool will work with all temporary file paths like /var/folders/123/abc/T/TemporaryItems/NSIRD_screencaptureui_ZfB1tD/Screenshot.png
- If you read a file that exists but has empty contents you will receive a system reminder warning in place of file contents.`
};

/**
 * 权限规则行为类型
 * @original e6Q数组
 */
const PERMISSION_BEHAVIORS = ["allow", "deny"];

/**
 * 解析权限规则
 * @param {Object} config - 配置对象
 * @param {string} source - 规则来源
 * @returns {Array} 权限规则数组
 * @original A8Q函数
 */
export function parsePermissionRules(config, source) {
  if (!config || !config.permissions) {
    return [];
  }
  
  const { permissions } = config;
  const rules = [];
  
  for (const behavior of PERMISSION_BEHAVIORS) {
    const behaviorRules = permissions[behavior];
    if (behaviorRules) {
      for (const rule of behaviorRules) {
        rules.push({
          source,
          ruleBehavior: behavior,
          ruleValue: normalizeRule(rule)
        });
      }
    }
  }
  
  return rules;
}

/**
 * 获取被阻止的工具列表
 * @param {Object} config - 配置对象
 * @param {Array} permissionRules - 权限规则数组
 * @returns {Array} 被阻止的工具列表
 * @original B8Q函数
 */
export function getBlockedTools(config, permissionRules) {
  if (!config.allowedTools || config.allowedTools.length < 1) {
    return [];
  }
  
  // 获取本地设置中允许的工具
  const locallyAllowedTools = new Set();
  for (const rule of permissionRules) {
    if (rule.ruleBehavior === "allow" && rule.source === "localSettings") {
      locallyAllowedTools.add(getToolName(rule.ruleValue));
    }
  }
  
  // 找出配置中允许但本地设置中未允许的工具
  const blockedTools = new Set();
  for (const tool of config.allowedTools) {
    if (!locallyAllowedTools.has(tool)) {
      blockedTools.add(tool);
    }
  }
  
  return Array.from(blockedTools);
}

/**
 * 获取被阻止的忽略模式
 * @param {Object} config - 配置对象
 * @param {Array} permissionRules - 权限规则数组
 * @returns {Array} 被阻止的忽略模式数组
 * @original Q8Q函数
 */
export function getBlockedIgnorePatterns(config, permissionRules) {
  if (!config.ignorePatterns || config.ignorePatterns.length < 1) {
    return [];
  }
  
  // 获取本地设置中拒绝的读取规则
  const locallyDeniedPatterns = new Set();
  for (const rule of permissionRules) {
    if (rule.ruleBehavior === "deny" && 
        rule.source === "localSettings" && 
        rule.ruleValue.toolName === FILE_READ_TOOL.name && 
        rule.ruleValue.ruleContent !== undefined) {
      locallyDeniedPatterns.add(rule.ruleValue.ruleContent);
    }
  }
  
  // 找出配置中的忽略模式但本地设置中未拒绝的
  const blockedPatterns = new Set();
  for (const pattern of config.ignorePatterns) {
    if (!locallyDeniedPatterns.has(pattern)) {
      blockedPatterns.add(pattern);
    }
  }
  
  return Array.from(blockedPatterns).map(pattern => ({
    toolName: FILE_READ_TOOL.name,
    ruleContent: pattern
  }));
}

/**
 * 文件读取服务类
 * @description 提供文件读取功能的完整实现
 */
export class FileReadService {
  constructor(options = {}) {
    this.maxLines = options.maxLines || FILE_READ_TOOL.maxLines;
    this.maxLineLength = options.maxLineLength || FILE_READ_TOOL.maxLineLength;
    this.allowedTools = new Set(options.allowedTools || []);
    this.ignorePatterns = options.ignorePatterns || [];
    this.permissionRules = options.permissionRules || [];
  }

  /**
   * 检查工具是否被允许
   * @param {string} toolName - 工具名称
   * @returns {boolean} 是否被允许
   */
  isToolAllowed(toolName) {
    if (this.allowedTools.size === 0) {
      return true; // 如果没有限制，默认允许
    }
    return this.allowedTools.has(toolName);
  }

  /**
   * 检查文件路径是否匹配忽略模式
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否匹配忽略模式
   */
  matchesIgnorePattern(filePath) {
    for (const pattern of this.ignorePatterns) {
      if (this.matchPattern(filePath, pattern)) {
        return true;
      }
    }
    return false;
  }

  /**
   * 模式匹配
   * @param {string} text - 要匹配的文本
   * @param {string} pattern - 模式
   * @returns {boolean} 是否匹配
   * @private
   */
  matchPattern(text, pattern) {
    // 简单的glob模式匹配实现
    const regexPattern = pattern
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.')
      .replace(/\[([^\]]+)\]/g, '[$1]');
    
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(text);
  }

  /**
   * 验证文件读取请求
   * @param {string} filePath - 文件路径
   * @param {Object} options - 读取选项
   * @returns {Object} 验证结果
   */
  validateReadRequest(filePath, options = {}) {
    const result = {
      valid: true,
      errors: [],
      warnings: []
    };

    // 检查工具是否被允许
    if (!this.isToolAllowed(FILE_READ_TOOL.name)) {
      result.valid = false;
      result.errors.push(`Tool '${FILE_READ_TOOL.name}' is not allowed`);
    }

    // 检查文件路径是否匹配忽略模式
    if (this.matchesIgnorePattern(filePath)) {
      result.valid = false;
      result.errors.push(`File path '${filePath}' matches ignore pattern`);
    }

    // 检查路径是否为绝对路径
    if (!filePath.startsWith('/') && !filePath.match(/^[A-Za-z]:/)) {
      result.warnings.push('File path should be absolute for better reliability');
    }

    // 检查行数限制
    if (options.limit && options.limit > this.maxLines) {
      result.warnings.push(`Requested limit (${options.limit}) exceeds maximum (${this.maxLines})`);
    }

    return result;
  }

  /**
   * 格式化读取选项
   * @param {Object} options - 原始选项
   * @returns {Object} 格式化后的选项
   */
  formatReadOptions(options = {}) {
    return {
      offset: Math.max(0, options.offset || 0),
      limit: Math.min(this.maxLines, options.limit || this.maxLines),
      maxLineLength: Math.min(this.maxLineLength, options.maxLineLength || this.maxLineLength),
      encoding: options.encoding || 'utf8',
      showLineNumbers: options.showLineNumbers !== false
    };
  }

  /**
   * 处理文件内容
   * @param {string} content - 文件内容
   * @param {Object} options - 处理选项
   * @returns {string} 处理后的内容
   */
  processFileContent(content, options = {}) {
    const formattedOptions = this.formatReadOptions(options);
    const lines = content.split('\n');
    
    // 应用偏移和限制
    const startIndex = formattedOptions.offset;
    const endIndex = Math.min(lines.length, startIndex + formattedOptions.limit);
    const selectedLines = lines.slice(startIndex, endIndex);
    
    // 处理每一行
    const processedLines = selectedLines.map((line, index) => {
      // 截断过长的行
      let processedLine = line.length > formattedOptions.maxLineLength ? 
        line.substring(0, formattedOptions.maxLineLength) + '...' : 
        line;
      
      // 添加行号
      if (formattedOptions.showLineNumbers) {
        const lineNumber = startIndex + index + 1;
        processedLine = `${lineNumber.toString().padStart(4, ' ')}\t${processedLine}`;
      }
      
      return processedLine;
    });
    
    return processedLines.join('\n');
  }

  /**
   * 获取服务配置
   * @returns {Object} 服务配置
   */
  getConfiguration() {
    return {
      maxLines: this.maxLines,
      maxLineLength: this.maxLineLength,
      allowedTools: Array.from(this.allowedTools),
      ignorePatterns: [...this.ignorePatterns],
      permissionRulesCount: this.permissionRules.length
    };
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfiguration(newConfig) {
    if (newConfig.maxLines !== undefined) {
      this.maxLines = newConfig.maxLines;
    }
    if (newConfig.maxLineLength !== undefined) {
      this.maxLineLength = newConfig.maxLineLength;
    }
    if (newConfig.allowedTools !== undefined) {
      this.allowedTools = new Set(newConfig.allowedTools);
    }
    if (newConfig.ignorePatterns !== undefined) {
      this.ignorePatterns = [...newConfig.ignorePatterns];
    }
    if (newConfig.permissionRules !== undefined) {
      this.permissionRules = [...newConfig.permissionRules];
    }
  }

  /**
   * 添加忽略模式
   * @param {string} pattern - 忽略模式
   */
  addIgnorePattern(pattern) {
    if (!this.ignorePatterns.includes(pattern)) {
      this.ignorePatterns.push(pattern);
    }
  }

  /**
   * 移除忽略模式
   * @param {string} pattern - 忽略模式
   */
  removeIgnorePattern(pattern) {
    const index = this.ignorePatterns.indexOf(pattern);
    if (index > -1) {
      this.ignorePatterns.splice(index, 1);
    }
  }

  /**
   * 添加允许的工具
   * @param {string} toolName - 工具名称
   */
  addAllowedTool(toolName) {
    this.allowedTools.add(toolName);
  }

  /**
   * 移除允许的工具
   * @param {string} toolName - 工具名称
   */
  removeAllowedTool(toolName) {
    this.allowedTools.delete(toolName);
  }
}

// 辅助函数

/**
 * 规范化规则
 * @param {*} rule - 原始规则
 * @returns {*} 规范化后的规则
 * @original Az函数的实现（推测）
 */
function normalizeRule(rule) {
  // @todo: 实现Az函数的实际逻辑
  return rule;
}

/**
 * 获取工具名称
 * @param {*} ruleValue - 规则值
 * @returns {string} 工具名称
 * @original d5函数的实现（推测）
 */
function getToolName(ruleValue) {
  // @todo: 实现d5函数的实际逻辑
  return ruleValue.toolName || ruleValue;
}

// 创建默认文件读取服务实例
export const fileReadService = new FileReadService();
