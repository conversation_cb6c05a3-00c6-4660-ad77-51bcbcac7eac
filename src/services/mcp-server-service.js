/**
 * MCP服务器管理服务
 * @description 重构自原始文件中的MCP服务器管理代码，对应第4851-4900行
 * @original 原始代码行 4851-4900
 */

import { join } from "path";
import { homedir } from "os";

/**
 * 写入项目MCP配置
 * @param {Object} config - MCP配置对象
 * @original NYA函数
 */
export function writeProjectMCPConfig(config) {
  const configPath = join(getCurrentWorkingDirectory(), ".mcp.json");
  writeFileSync(configPath, JSON.stringify(config, null, 2), {
    encoding: "utf8"
  });
}

/**
 * 验证MCP服务器配置
 * @param {string} name - 服务器名称
 * @param {Object} config - 服务器配置
 * @param {string} scope - 配置作用域
 * @throws {Error} 验证失败时抛出错误
 * @original Hh函数
 */
export function validateMCPServerConfig(name, config, scope) {
  // 验证服务器名称
  if (name.match(/[^a-zA-Z0-9_-]/)) {
    throw new Error(`Invalid name ${name}. Names can only contain letters, numbers, hyphens, and underscores.`);
  }

  // 验证配置结构
  const validationResult = validateServerConfigSchema(config);
  if (!validationResult.success) {
    const errorMessages = validationResult.error.errors
      .map(error => `${error.path.join(".")}: ${error.message}`)
      .join(", ");
    throw new Error(`Invalid configuration: ${errorMessages}`);
  }

  // 检查服务器是否已存在
  switch (scope) {
    case "project":
      {
        const { servers } = getProjectConfig("project");
        if (servers[name]) {
          throw new Error(`MCP server ${name} already exists in .mcp.json`);
        }
        break;
      }
    case "user":
      {
        const userConfig = getUserConfig();
        if (userConfig.mcpServers?.[name]) {
          throw new Error(`MCP server ${name} already exists in user config`);
        }
        break;
      }
    case "local":
      {
        const localConfig = getLocalConfig();
        if (localConfig.mcpServers?.[name]) {
          throw new Error(`MCP server ${name} already exists in local config`);
        }
        break;
      }
    case "dynamic":
      throw new Error("Cannot add MCP server to scope: dynamic");
  }
}

/**
 * 添加MCP服务器到项目配置
 * @param {string} name - 服务器名称
 * @param {Object} config - 服务器配置
 * @param {string} scope - 配置作用域
 * @returns {Object} 更新后的配置
 * @original Hh函数的后半部分
 */
export function addMCPServerToProject(name, config, scope) {
  // 验证配置
  validateMCPServerConfig(name, config, scope);
  
  const validatedConfig = validateServerConfigSchema(config).data;

  switch (scope) {
    case "project":
      {
        const { servers } = getProjectConfig("project");
        const cleanedServers = {};
        
        // 清理现有服务器配置（移除scope字段）
        for (const [serverName, serverConfig] of Object.entries(servers)) {
          const { scope: _, ...cleanConfig } = serverConfig;
          cleanedServers[serverName] = cleanConfig;
        }
        
        // 添加新服务器
        cleanedServers[name] = validatedConfig;
        
        const updatedConfig = {
          mcpServers: cleanedServers
        };
        
        writeProjectMCPConfig(updatedConfig);
        return updatedConfig;
      }
    case "user":
      {
        const userConfig = getUserConfig();
        const updatedConfig = {
          ...userConfig,
          mcpServers: {
            ...userConfig.mcpServers,
            [name]: validatedConfig
          }
        };
        
        saveUserConfig(updatedConfig);
        return updatedConfig;
      }
    case "local":
      {
        const localConfig = getLocalConfig();
        const updatedConfig = {
          ...localConfig,
          mcpServers: {
            ...localConfig.mcpServers,
            [name]: validatedConfig
          }
        };
        
        saveLocalConfig(updatedConfig);
        return updatedConfig;
      }
    default:
      throw new Error(`Unsupported scope: ${scope}`);
  }
}

/**
 * MCP服务器管理服务类
 * @description 提供完整的MCP服务器管理功能
 */
export class MCPServerService {
  constructor() {
    this.servers = new Map();
    this.configCache = new Map();
    this.cacheTimeout = 60000; // 1分钟缓存
  }

  /**
   * 添加MCP服务器
   * @param {string} name - 服务器名称
   * @param {Object} config - 服务器配置
   * @param {string} scope - 配置作用域
   * @returns {Object} 添加结果
   */
  addServer(name, config, scope = "project") {
    try {
      const updatedConfig = addMCPServerToProject(name, config, scope);
      
      // 清除相关缓存
      this.clearConfigCache(scope);
      
      return {
        success: true,
        config: updatedConfig,
        message: `MCP server ${name} added successfully to ${scope} scope`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 移除MCP服务器
   * @param {string} name - 服务器名称
   * @param {string} scope - 配置作用域
   * @returns {Object} 移除结果
   */
  removeServer(name, scope = "project") {
    try {
      switch (scope) {
        case "project":
          {
            const { servers } = getProjectConfig("project");
            if (!servers[name]) {
              throw new Error(`MCP server ${name} not found in project config`);
            }
            
            const { [name]: removed, ...remainingServers } = servers;
            const updatedConfig = {
              mcpServers: remainingServers
            };
            
            writeProjectMCPConfig(updatedConfig);
            this.clearConfigCache(scope);
            
            return {
              success: true,
              config: updatedConfig,
              message: `MCP server ${name} removed from ${scope} scope`
            };
          }
        case "user":
          {
            const userConfig = getUserConfig();
            if (!userConfig.mcpServers?.[name]) {
              throw new Error(`MCP server ${name} not found in user config`);
            }
            
            const { [name]: removed, ...remainingServers } = userConfig.mcpServers;
            const updatedConfig = {
              ...userConfig,
              mcpServers: remainingServers
            };
            
            saveUserConfig(updatedConfig);
            this.clearConfigCache(scope);
            
            return {
              success: true,
              config: updatedConfig,
              message: `MCP server ${name} removed from ${scope} scope`
            };
          }
        case "local":
          {
            const localConfig = getLocalConfig();
            if (!localConfig.mcpServers?.[name]) {
              throw new Error(`MCP server ${name} not found in local config`);
            }
            
            const { [name]: removed, ...remainingServers } = localConfig.mcpServers;
            const updatedConfig = {
              ...localConfig,
              mcpServers: remainingServers
            };
            
            saveLocalConfig(updatedConfig);
            this.clearConfigCache(scope);
            
            return {
              success: true,
              config: updatedConfig,
              message: `MCP server ${name} removed from ${scope} scope`
            };
          }
        default:
          throw new Error(`Unsupported scope: ${scope}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取MCP服务器列表
   * @param {string} scope - 配置作用域（可选）
   * @returns {Array} 服务器列表
   */
  getServers(scope) {
    const servers = [];
    
    if (!scope || scope === "project") {
      try {
        const { servers: projectServers } = getProjectConfig("project");
        for (const [name, config] of Object.entries(projectServers)) {
          servers.push({
            name,
            config,
            scope: "project"
          });
        }
      } catch {
        // 忽略项目配置错误
      }
    }
    
    if (!scope || scope === "user") {
      try {
        const userConfig = getUserConfig();
        if (userConfig.mcpServers) {
          for (const [name, config] of Object.entries(userConfig.mcpServers)) {
            servers.push({
              name,
              config,
              scope: "user"
            });
          }
        }
      } catch {
        // 忽略用户配置错误
      }
    }
    
    if (!scope || scope === "local") {
      try {
        const localConfig = getLocalConfig();
        if (localConfig.mcpServers) {
          for (const [name, config] of Object.entries(localConfig.mcpServers)) {
            servers.push({
              name,
              config,
              scope: "local"
            });
          }
        }
      } catch {
        // 忽略本地配置错误
      }
    }
    
    return servers;
  }

  /**
   * 获取特定MCP服务器配置
   * @param {string} name - 服务器名称
   * @param {string} scope - 配置作用域（可选）
   * @returns {Object|null} 服务器配置或null
   */
  getServer(name, scope) {
    const servers = this.getServers(scope);
    return servers.find(server => server.name === name) || null;
  }

  /**
   * 验证服务器配置
   * @param {string} name - 服务器名称
   * @param {Object} config - 服务器配置
   * @param {string} scope - 配置作用域
   * @returns {Object} 验证结果
   */
  validateServer(name, config, scope) {
    try {
      validateMCPServerConfig(name, config, scope);
      return {
        valid: true,
        message: "Configuration is valid"
      };
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  /**
   * 清除配置缓存
   * @param {string} scope - 配置作用域
   * @private
   */
  clearConfigCache(scope) {
    if (scope) {
      this.configCache.delete(scope);
    } else {
      this.configCache.clear();
    }
  }

  /**
   * 移除MCP服务器（扩展版本）
   * @param {string} name - 服务器名称
   * @param {string} scope - 配置作用域
   * @returns {Object} 移除结果
   * @original br1函数
   */
  removeMCPServer(name, scope) {
    try {
      switch (scope) {
        case "project":
          {
            const { servers } = getProjectConfig("project");
            if (!servers[name]) {
              throw new Error(`No MCP server found with name: ${name} in .mcp.json`);
            }

            const cleanedServers = {};
            for (const [serverName, serverConfig] of Object.entries(servers)) {
              if (serverName !== name) {
                const { scope: _, ...cleanConfig } = serverConfig;
                cleanedServers[serverName] = cleanConfig;
              }
            }

            const updatedConfig = {
              mcpServers: cleanedServers
            };

            writeProjectMCPConfig(updatedConfig);
            this.clearConfigCache(scope);

            return {
              success: true,
              config: updatedConfig,
              message: `MCP server ${name} removed from project config`
            };
          }
        case "user":
          {
            const userConfig = getUserConfig();
            if (!userConfig.mcpServers?.[name]) {
              throw new Error(`No user-scoped MCP server found with name: ${name}`);
            }

            delete userConfig.mcpServers[name];
            saveUserConfig(userConfig);
            this.clearConfigCache(scope);

            return {
              success: true,
              config: userConfig,
              message: `MCP server ${name} removed from user config`
            };
          }
        case "local":
          {
            const localConfig = getLocalConfig();
            if (!localConfig.mcpServers?.[name]) {
              throw new Error(`No project-local MCP server found with name: ${name}`);
            }

            delete localConfig.mcpServers[name];
            saveLocalConfig(localConfig);
            this.clearConfigCache(scope);

            return {
              success: true,
              config: localConfig,
              message: `MCP server ${name} removed from local config`
            };
          }
        default:
          throw new Error(`Cannot remove MCP server from scope: ${scope}`);
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 查找MCP服务器配置（按优先级）
   * @param {string} name - 服务器名称
   * @returns {Object|null} 服务器配置或null
   * @original gi函数
   */
  findServerByPriority(name) {
    try {
      // 按优先级查找：local > project > user
      const { servers: localServers } = getProjectConfig("local");
      if (localServers[name]) {
        return {
          name,
          config: localServers[name],
          scope: "local"
        };
      }

      const { servers: projectServers } = getProjectConfig("project");
      if (projectServers[name]) {
        return {
          name,
          config: projectServers[name],
          scope: "project"
        };
      }

      const { servers: userServers } = getProjectConfig("user");
      if (userServers[name]) {
        return {
          name,
          config: userServers[name],
          scope: "user"
        };
      }

      return null;
    } catch {
      return null;
    }
  }

  /**
   * 获取已批准的MCP服务器
   * @returns {Object} 已批准的服务器映射
   * @original Qz函数
   */
  getApprovedServers() {
    try {
      const { servers: userServers } = getProjectConfig("user");
      const { servers: projectServers } = getProjectConfig("project");
      const { servers: localServers } = getProjectConfig("local");

      const approvedServers = {};

      // 检查用户服务器
      for (const [name, config] of Object.entries(userServers)) {
        if (this.getServerApprovalStatus(name) === "approved") {
          approvedServers[name] = config;
        }
      }

      // 检查项目服务器
      for (const [name, config] of Object.entries(projectServers)) {
        if (this.getServerApprovalStatus(name) === "approved") {
          approvedServers[name] = config;
        }
      }

      // 检查本地服务器
      for (const [name, config] of Object.entries(localServers)) {
        if (this.getServerApprovalStatus(name) === "approved") {
          approvedServers[name] = config;
        }
      }

      return approvedServers;
    } catch {
      return {};
    }
  }

  /**
   * 获取服务器批准状态
   * @param {string} name - 服务器名称
   * @returns {string} 批准状态
   * @original EK1函数
   */
  getServerApprovalStatus(name) {
    // @todo: 实现EK1函数的实际逻辑
    // 这里返回简化的状态检查
    const server = this.findServerByPriority(name);
    if (!server) {
      return "not_found";
    }

    // 简化的批准逻辑：如果服务器存在且有有效配置，则认为已批准
    if (server.config && server.config.command) {
      return "approved";
    }

    return "pending";
  }

  /**
   * 批准MCP服务器
   * @param {string} name - 服务器名称
   * @param {string} scope - 配置作用域
   * @returns {Object} 批准结果
   */
  approveServer(name, scope = "user") {
    try {
      const server = this.findServerByPriority(name);
      if (!server) {
        throw new Error(`MCP server ${name} not found`);
      }

      // 这里可以添加批准逻辑，比如设置批准标志
      const updatedConfig = {
        ...server.config,
        approved: true,
        approvedAt: new Date().toISOString()
      };

      return this.addServer(name, updatedConfig, scope);
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 拒绝MCP服务器
   * @param {string} name - 服务器名称
   * @param {string} reason - 拒绝原因
   * @returns {Object} 拒绝结果
   */
  rejectServer(name, reason = "User rejected") {
    try {
      const server = this.findServerByPriority(name);
      if (!server) {
        throw new Error(`MCP server ${name} not found`);
      }

      // 移除服务器或标记为拒绝
      return this.removeMCPServer(name, server.scope);
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取服务器统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const servers = this.getServers();
    const approvedServers = this.getApprovedServers();
    const scopeStats = {};
    const statusStats = {
      approved: 0,
      pending: 0,
      rejected: 0
    };

    for (const server of servers) {
      scopeStats[server.scope] = (scopeStats[server.scope] || 0) + 1;

      const status = this.getServerApprovalStatus(server.name);
      if (statusStats[status] !== undefined) {
        statusStats[status]++;
      }
    }

    return {
      totalServers: servers.length,
      approvedServers: Object.keys(approvedServers).length,
      scopeStats,
      statusStats,
      servers: servers.map(server => ({
        name: server.name,
        scope: server.scope,
        status: this.getServerApprovalStatus(server.name),
        hasCommand: !!server.config.command,
        hasArgs: !!server.config.args,
        hasEnv: !!server.config.env,
        approved: !!server.config.approved
      }))
    };
  }

  /**
   * 导出所有服务器配置
   * @returns {Object} 导出的配置
   */
  exportAllConfigs() {
    return {
      user: this.getServers("user"),
      project: this.getServers("project"),
      local: this.getServers("local"),
      approved: this.getApprovedServers(),
      stats: this.getStats()
    };
  }

  /**
   * 导入服务器配置
   * @param {Object} configs - 配置对象
   * @param {Object} options - 导入选项
   * @returns {Object} 导入结果
   */
  importConfigs(configs, options = {}) {
    const {
      overwrite = false,
      scope = "user"
    } = options;

    const results = {
      success: [],
      failed: [],
      skipped: []
    };

    for (const [name, config] of Object.entries(configs)) {
      try {
        const existing = this.getServer(name, scope);

        if (existing && !overwrite) {
          results.skipped.push({
            name,
            reason: "Server already exists and overwrite is disabled"
          });
          continue;
        }

        const result = this.addServer(name, config, scope);

        if (result.success) {
          results.success.push(name);
        } else {
          results.failed.push({
            name,
            error: result.error
          });
        }
      } catch (error) {
        results.failed.push({
          name,
          error: error.message
        });
      }
    }

    return results;
  }
}

// 辅助函数

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 同步写入文件
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @param {Object} options - 写入选项
 * @original kN函数的实现（推测）
 */
function writeFileSync(filePath, content, options) {
  const fs = require('fs');
  fs.writeFileSync(filePath, content, options);
}

/**
 * 验证服务器配置结构
 * @param {Object} config - 配置对象
 * @returns {Object} 验证结果
 * @original Cr1.safeParse的实现（推测）
 */
function validateServerConfigSchema(config) {
  // @todo: 实现Cr1.safeParse的实际逻辑
  // 这里返回简化的验证结果
  if (!config || typeof config !== 'object') {
    return {
      success: false,
      error: {
        errors: [{ path: [], message: 'Configuration must be an object' }]
      }
    };
  }
  
  if (!config.command) {
    return {
      success: false,
      error: {
        errors: [{ path: ['command'], message: 'command is required' }]
      }
    };
  }
  
  return {
    success: true,
    data: config
  };
}

/**
 * 获取项目配置
 * @param {string} scope - 配置作用域
 * @returns {Object} 项目配置
 * @original pZ函数的实现（推测）
 */
function getProjectConfig(scope) {
  // @todo: 实现pZ函数的实际逻辑
  return { servers: {} };
}

/**
 * 获取用户配置
 * @returns {Object} 用户配置
 * @original E0函数的实现（推测）
 */
function getUserConfig() {
  // @todo: 实现E0函数的实际逻辑
  return { mcpServers: {} };
}

/**
 * 获取本地配置
 * @returns {Object} 本地配置
 * @original t9函数的实现（推测）
 */
function getLocalConfig() {
  // @todo: 实现t9函数的实际逻辑
  return { mcpServers: {} };
}

/**
 * 保存用户配置
 * @param {Object} config - 配置对象
 * @original 保存用户配置的实现（推测）
 */
function saveUserConfig(config) {
  // @todo: 实现保存用户配置的实际逻辑
}

/**
 * 保存本地配置
 * @param {Object} config - 配置对象
 * @original 保存本地配置的实现（推测）
 */
function saveLocalConfig(config) {
  // @todo: 实现保存本地配置的实际逻辑
}

// 创建默认MCP服务器服务实例
export const mcpServerService = new MCPServerService();
