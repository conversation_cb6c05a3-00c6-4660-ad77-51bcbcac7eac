/**
 * 设置管理服务
 * @description 重构自原始文件中的设置管理代码，对应第5102-5200行
 * @original 原始代码行 5102-5200
 */

import { dirname } from "path";
import { fileWatcherService } from './file-watcher-service.js';

/**
 * 写入设置到文件
 * @param {string} source - 设置源
 * @param {Object} settings - 设置对象
 * @returns {Object} 写入结果
 * @original f8函数
 */
export function writeSettingsToFile(source, settings) {
  // 策略设置和标志设置不需要写入文件
  if (source === "policySettings" || source === "flagSettings") {
    return {
      error: null
    };
  }

  const filePath = getConfigPath(source);
  if (!filePath) {
    return {
      error: null
    };
  }

  try {
    // 确保目录存在
    const dir = dirname(filePath);
    const fs = getFileSystem();
    
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    // 获取当前设置
    let currentSettings = getSettingsContent(source);
    
    // 如果文件存在但无法解析，尝试读取原始内容
    if (!currentSettings && fs.existsSync(filePath)) {
      const rawContent = readFileContent(filePath);
      const parsedContent = parseJSON(rawContent);
      
      if (parsedContent === null) {
        return {
          error: new Error(`Invalid JSON syntax in settings file at ${filePath}`)
        };
      }
      
      if (parsedContent && typeof parsedContent === 'object') {
        currentSettings = parsedContent;
        console.log(`Using raw settings from ${filePath} due to validation failure`);
      }
    }

    // 合并设置
    const mergedSettings = mergeSettings(currentSettings || {}, settings, (oldValue, newValue, key, parent) => {
      // 如果新值为undefined且父对象存在，删除该键
      if (newValue === undefined && parent && typeof key === "string") {
        delete parent[key];
        return;
      }
      
      // 如果新值是数组，直接返回
      if (Array.isArray(newValue)) {
        return newValue;
      }
      
      return;
    });

    // 标记内部写入
    fileWatcherService.markInternalWrite(source);
    
    // 写入文件
    writeFileSync(filePath, JSON.stringify(mergedSettings, null, 2));
    
    // 清除缓存
    clearSettingsCache();
    
    // 如果是本地设置，添加到Git忽略
    if (source === "localSettings") {
      const { addToGitIgnore } = require('../utils/git-ignore-utils.js');
      addToGitIgnore(getConfigFileName("localSettings"), getCurrentWorkingDirectory());
    }
    
  } catch (error) {
    const wrappedError = new Error(`Failed to read raw settings from ${filePath}: ${error}`);
    logError(wrappedError);
    return {
      error: wrappedError
    };
  }

  return {
    error: null
  };
}

/**
 * 清除设置缓存
 * @original l91函数
 */
export function clearSettingsCache() {
  // 清除全局设置缓存
  if (global.settingsCache) {
    global.settingsCache = null;
  }
}

/**
 * 创建带缓存的异步函数
 * @param {Function} fn - 要缓存的函数
 * @param {number} ttl - 缓存生存时间（毫秒）
 * @returns {Function} 带缓存的函数
 * @original AWA函数
 */
export function createCachedAsyncFunction(fn, ttl = 300000) {
  const cache = new Map();
  
  const cachedFunction = async (...args) => {
    const key = JSON.stringify(args);
    const cached = cache.get(key);
    const now = Date.now();
    
    // 如果没有缓存，执行函数并缓存结果
    if (!cached) {
      const result = await fn(...args);
      cache.set(key, {
        value: result,
        timestamp: now,
        refreshing: false
      });
      return result;
    }
    
    // 如果缓存过期且没有在刷新，后台刷新缓存
    if (cached && now - cached.timestamp > ttl && !cached.refreshing) {
      cached.refreshing = true;
      
      fn(...args).then(result => {
        cache.set(key, {
          value: result,
          timestamp: Date.now(),
          refreshing: false
        });
      }).catch(error => {
        logError(error instanceof Error ? error : new Error(String(error)));
        const currentCached = cache.get(key);
        if (currentCached) {
          currentCached.refreshing = false;
        }
      });
      
      return cached.value;
    }
    
    return cache.get(key).value;
  };
  
  // 添加缓存控制方法
  cachedFunction.cache = {
    clear: () => cache.clear()
  };
  
  return cachedFunction;
}

/**
 * 创建带LRU缓存的同步函数
 * @param {Function} fn - 要缓存的函数
 * @param {Function} keyGenerator - 键生成函数
 * @returns {Function} 带缓存的函数
 * @original pK1函数
 */
export function createLRUCachedFunction(fn, keyGenerator) {
  const cache = new Map(); // 简化的LRU实现
  const maxSize = 1000;
  
  const cachedFunction = (...args) => {
    const key = keyGenerator(...args);
    const cached = cache.get(key);
    
    if (cached !== undefined) {
      return cached;
    }
    
    const result = fn(...args);
    
    // 简单的LRU逻辑：如果超过最大大小，删除最旧的条目
    if (cache.size >= maxSize) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    
    cache.set(key, result);
    return result;
  };
  
  // 添加缓存控制方法
  cachedFunction.cache = {
    clear: () => cache.clear(),
    size: () => cache.size
  };
  
  return cachedFunction;
}

/**
 * 设置管理服务类
 * @description 提供完整的设置管理功能
 */
export class SettingsService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 60000; // 1分钟缓存
    this.watchers = new Set();
  }

  /**
   * 获取设置
   * @param {string} source - 设置源
   * @param {string} key - 设置键（可选）
   * @returns {*} 设置值
   */
  get(source, key) {
    const settings = this.getSettings(source);
    
    if (!key) {
      return settings;
    }
    
    return this.getNestedValue(settings, key);
  }

  /**
   * 设置值
   * @param {string} source - 设置源
   * @param {string} key - 设置键
   * @param {*} value - 设置值
   * @returns {Object} 设置结果
   */
  set(source, key, value) {
    const currentSettings = this.getSettings(source) || {};
    const updatedSettings = this.setNestedValue(currentSettings, key, value);
    
    return writeSettingsToFile(source, updatedSettings);
  }

  /**
   * 删除设置
   * @param {string} source - 设置源
   * @param {string} key - 设置键
   * @returns {Object} 删除结果
   */
  delete(source, key) {
    const currentSettings = this.getSettings(source) || {};
    const updatedSettings = this.deleteNestedValue(currentSettings, key);
    
    return writeSettingsToFile(source, updatedSettings);
  }

  /**
   * 获取所有设置源的设置
   * @returns {Object} 所有设置
   */
  getAllSettings() {
    const sources = ["userSettings", "projectSettings", "localSettings"];
    const allSettings = {};
    
    for (const source of sources) {
      allSettings[source] = this.getSettings(source);
    }
    
    return allSettings;
  }

  /**
   * 合并多个设置源
   * @param {Array} sources - 设置源数组（按优先级排序）
   * @returns {Object} 合并后的设置
   */
  mergeSettings(sources = ["userSettings", "projectSettings", "localSettings"]) {
    let merged = {};
    
    for (const source of sources) {
      const settings = this.getSettings(source);
      if (settings) {
        merged = mergeSettings(merged, settings);
      }
    }
    
    return merged;
  }

  /**
   * 监听设置变更
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消监听函数
   */
  watch(callback) {
    this.watchers.add(callback);
    
    return () => {
      this.watchers.delete(callback);
    };
  }

  /**
   * 清除所有缓存
   */
  clearCache() {
    this.cache.clear();
    clearSettingsCache();
  }

  /**
   * 获取设置（带缓存）
   * @param {string} source - 设置源
   * @returns {Object|null} 设置对象
   * @private
   */
  getSettings(source) {
    const cacheKey = `settings:${source}`;
    const cached = this.getFromCache(cacheKey);
    
    if (cached !== null) {
      return cached;
    }

    const settings = getSettingsContent(source);
    this.setCache(cacheKey, settings);
    
    return settings;
  }

  /**
   * 获取嵌套值
   * @param {Object} obj - 对象
   * @param {string} key - 键路径（用.分隔）
   * @returns {*} 值
   * @private
   */
  getNestedValue(obj, key) {
    if (!obj || typeof obj !== 'object') {
      return undefined;
    }
    
    const keys = key.split('.');
    let current = obj;
    
    for (const k of keys) {
      if (current === null || current === undefined) {
        return undefined;
      }
      current = current[k];
    }
    
    return current;
  }

  /**
   * 设置嵌套值
   * @param {Object} obj - 对象
   * @param {string} key - 键路径（用.分隔）
   * @param {*} value - 值
   * @returns {Object} 更新后的对象
   * @private
   */
  setNestedValue(obj, key, value) {
    const result = { ...obj };
    const keys = key.split('.');
    let current = result;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!current[k] || typeof current[k] !== 'object') {
        current[k] = {};
      }
      current = current[k];
    }
    
    current[keys[keys.length - 1]] = value;
    return result;
  }

  /**
   * 删除嵌套值
   * @param {Object} obj - 对象
   * @param {string} key - 键路径（用.分隔）
   * @returns {Object} 更新后的对象
   * @private
   */
  deleteNestedValue(obj, key) {
    const result = { ...obj };
    const keys = key.split('.');
    let current = result;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!current[k] || typeof current[k] !== 'object') {
        return result; // 路径不存在
      }
      current = current[k];
    }
    
    delete current[keys[keys.length - 1]];
    return result;
  }

  /**
   * 从缓存获取结果
   * @param {string} key - 缓存键
   * @returns {*} 缓存的结果或null
   * @private
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.result;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {*} result - 结果
   * @private
   */
  setCache(key, result) {
    this.cache.set(key, {
      result,
      timestamp: Date.now()
    });
  }
}

// 辅助函数

/**
 * 获取配置路径
 * @param {string} source - 配置源
 * @returns {string|null} 配置路径
 * @original bO函数的实现（推测）
 */
function getConfigPath(source) {
  const { join } = require('path');
  const { homedir } = require('os');
  
  switch (source) {
    case "localSettings":
      return join(process.cwd(), ".claude", "config.json");
    case "projectSettings":
      return join(process.cwd(), ".claude", "project.json");
    case "userSettings":
      return join(homedir(), ".claude", "config.json");
    default:
      return null;
  }
}

/**
 * 获取设置内容
 * @param {string} source - 配置源
 * @returns {Object|null} 设置内容
 * @original $Y函数的实现（推测）
 */
function getSettingsContent(source) {
  // 这个函数在file-watcher-service.js中已经实现
  const { getSettingsContent } = require('./file-watcher-service.js');
  return getSettingsContent(source);
}

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 * @original x1函数的实现（推测）
 */
function getFileSystem() {
  return require('fs');
}

/**
 * 读取文件内容
 * @param {string} filePath - 文件路径
 * @returns {string} 文件内容
 * @original LY函数的实现（推测）
 */
function readFileContent(filePath) {
  const fs = getFileSystem();
  return fs.readFileSync(filePath, 'utf8');
}

/**
 * 解析JSON
 * @param {string} content - JSON字符串
 * @returns {*} 解析结果或null
 * @original T7函数的实现（推测）
 */
function parseJSON(content) {
  try {
    return JSON.parse(content);
  } catch {
    return null;
  }
}

/**
 * 合并设置对象
 * @param {Object} target - 目标对象
 * @param {Object} source - 源对象
 * @param {Function} customizer - 自定义合并函数
 * @returns {Object} 合并后的对象
 * @original BW1函数的实现（推测）
 */
function mergeSettings(target, source, customizer) {
  // 简化的合并实现
  const result = { ...target };
  
  for (const [key, value] of Object.entries(source)) {
    if (customizer) {
      const customResult = customizer(result[key], value, key, result);
      if (customResult !== undefined) {
        result[key] = customResult;
        continue;
      }
    }
    
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      result[key] = mergeSettings(result[key] || {}, value, customizer);
    } else {
      result[key] = value;
    }
  }
  
  return result;
}

/**
 * 同步写入文件
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @original kN函数的实现（推测）
 */
function writeFileSync(filePath, content) {
  const fs = getFileSystem();
  fs.writeFileSync(filePath, content, 'utf8');
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original x9函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 获取配置文件名
 * @param {string} source - 配置源
 * @returns {string} 配置文件名
 * @original c91函数的实现（推测）
 */
function getConfigFileName(source) {
  switch (source) {
    case "localSettings":
      return ".claude";
    case "projectSettings":
      return "project.json";
    case "userSettings":
      return "config.json";
    default:
      return "config.json";
  }
}

/**
 * 记录错误
 * @param {Error} error - 错误对象
 * @original T1函数的实现（推测）
 */
function logError(error) {
  console.error('Settings error:', error);
}

// 创建默认设置服务实例
export const settingsService = new SettingsService();
