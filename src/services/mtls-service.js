/**
 * mTLS（双向TLS）配置服务
 * @description 重构自原始文件中的mTLS配置代码，对应第5538-5600行
 * @original 原始代码行 5538-5600
 */

import { Agent as HTTPSAgent } from "https";

/**
 * 获取mTLS配置
 * @returns {Object|undefined} mTLS配置或undefined
 * @original BP函数
 */
export const getMTLSConfig = createLazyLoader(() => {
  const config = {};
  
  // 加载客户端证书
  if (process.env.CLAUDE_CODE_CLIENT_CERT) {
    try {
      config.cert = getFileSystem().readFileSync(process.env.CLAUDE_CODE_CLIENT_CERT, {
        encoding: "utf8"
      });
      logInfo("mTLS: Loaded client certificate from CLAUDE_CODE_CLIENT_CERT");
    } catch (error) {
      logError(`mTLS: Failed to load client certificate: ${error}`);
    }
  }
  
  // 加载客户端私钥
  if (process.env.CLAUDE_CODE_CLIENT_KEY) {
    try {
      config.key = getFileSystem().readFileSync(process.env.CLAUDE_CODE_CLIENT_KEY, {
        encoding: "utf8"
      });
      logInfo("mTLS: Loaded client key from CLAUDE_CODE_CLIENT_KEY");
    } catch (error) {
      logError(`mTLS: Failed to load client key: ${error}`);
    }
  }
  
  // 设置私钥密码
  if (process.env.CLAUDE_CODE_CLIENT_KEY_PASSPHRASE) {
    config.passphrase = process.env.CLAUDE_CODE_CLIENT_KEY_PASSPHRASE;
    logInfo("mTLS: Using client key passphrase");
  }
  
  // 如果没有配置任何mTLS选项，返回undefined
  if (Object.keys(config).length === 0) {
    return undefined;
  }
  
  return config;
});

/**
 * 创建HTTPS代理
 * @returns {HTTPSAgent|undefined} HTTPS代理或undefined
 * @original xw2函数
 */
export const createHTTPSAgent = createLazyLoader(() => {
  const mtlsConfig = getMTLSConfig();
  
  if (!mtlsConfig) {
    return undefined;
  }
  
  const agentConfig = {
    ...mtlsConfig,
    keepAlive: true
  };
  
  logInfo("mTLS: Creating HTTPS agent with custom certificates");
  return new HTTPSAgent(agentConfig);
});

/**
 * 获取mTLS证书配置（用于其他HTTP客户端）
 * @returns {Object|undefined} 证书配置或undefined
 * @original vw2函数
 */
export function getMTLSCertConfig() {
  const mtlsConfig = getMTLSConfig();
  
  if (!mtlsConfig) {
    return undefined;
  }
  
  return {
    cert: mtlsConfig.cert,
    key: mtlsConfig.key,
    passphrase: mtlsConfig.passphrase
  };
}

/**
 * 获取Undici代理配置
 * @returns {Object} Undici代理配置
 * @original ag函数
 */
export function getUndiciAgentConfig() {
  const mtlsConfig = getMTLSConfig();
  
  if (!mtlsConfig) {
    return {};
  }
  
  const certConfig = {
    cert: mtlsConfig.cert,
    key: mtlsConfig.key,
    passphrase: mtlsConfig.passphrase
  };
  
  // 动态导入undici Agent
  const { Agent } = require('undici');
  
  const agent = new Agent({
    connect: certConfig,
    pipelining: 1
  });
  
  logInfo("mTLS: Created undici agent with custom certificates");
  
  return {
    dispatcher: agent
  };
}

/**
 * 检查并记录CA证书配置
 * @original bw2函数
 */
export function checkCAConfig() {
  if (!getMTLSConfig()) {
    return;
  }
  
  if (process.env.NODE_EXTRA_CA_CERTS) {
    logInfo("NODE_EXTRA_CA_CERTS detected - Node.js will automatically append to built-in CAs");
  }
}

/**
 * 创建连接池代理
 * @param {string} uri - 连接URI
 * @returns {Object} 连接池代理配置
 * @original gw2函数
 */
export const createPoolAgent = createLazyLoader((uri) => {
  const mtlsConfig = getMTLSConfig();
  
  const poolConfig = {
    uri,
    pipelining: 1
  };
  
  if (mtlsConfig) {
    poolConfig.connect = {
      cert: mtlsConfig.cert,
      key: mtlsConfig.key,
      passphrase: mtlsConfig.passphrase
    };
    
    logInfo("mTLS: Created connection pool with custom certificates");
  }
  
  return poolConfig;
});

/**
 * mTLS服务类
 * @description 提供完整的mTLS配置管理功能
 */
export class MTLSService {
  constructor() {
    this.configCache = null;
    this.agentCache = new Map();
  }

  /**
   * 检查是否启用了mTLS
   * @returns {boolean} 是否启用mTLS
   */
  isEnabled() {
    return !!getMTLSConfig();
  }

  /**
   * 获取mTLS配置
   * @returns {Object|null} mTLS配置
   */
  getConfig() {
    return getMTLSConfig() || null;
  }

  /**
   * 获取HTTPS代理
   * @returns {HTTPSAgent|null} HTTPS代理
   */
  getHTTPSAgent() {
    return createHTTPSAgent() || null;
  }

  /**
   * 获取证书配置
   * @returns {Object|null} 证书配置
   */
  getCertConfig() {
    return getMTLSCertConfig() || null;
  }

  /**
   * 获取Undici代理配置
   * @returns {Object} Undici代理配置
   */
  getUndiciConfig() {
    return getUndiciAgentConfig();
  }

  /**
   * 创建自定义HTTP客户端配置
   * @param {Object} baseConfig - 基础配置
   * @returns {Object} 完整的HTTP客户端配置
   */
  createHTTPConfig(baseConfig = {}) {
    const mtlsConfig = this.getConfig();
    
    if (!mtlsConfig) {
      return baseConfig;
    }
    
    return {
      ...baseConfig,
      httpsAgent: this.getHTTPSAgent(),
      cert: mtlsConfig.cert,
      key: mtlsConfig.key,
      passphrase: mtlsConfig.passphrase
    };
  }

  /**
   * 创建Axios配置
   * @param {Object} baseConfig - 基础配置
   * @returns {Object} Axios配置
   */
  createAxiosConfig(baseConfig = {}) {
    const httpsAgent = this.getHTTPSAgent();
    
    if (!httpsAgent) {
      return baseConfig;
    }
    
    return {
      ...baseConfig,
      httpsAgent
    };
  }

  /**
   * 创建Fetch配置
   * @param {Object} baseConfig - 基础配置
   * @returns {Object} Fetch配置
   */
  createFetchConfig(baseConfig = {}) {
    const undiciConfig = this.getUndiciConfig();
    
    if (!undiciConfig.dispatcher) {
      return baseConfig;
    }
    
    return {
      ...baseConfig,
      dispatcher: undiciConfig.dispatcher
    };
  }

  /**
   * 验证mTLS配置
   * @returns {Object} 验证结果
   */
  validateConfig() {
    const config = this.getConfig();
    
    if (!config) {
      return {
        valid: true,
        message: "mTLS not configured"
      };
    }
    
    const errors = [];
    
    if (!config.cert) {
      errors.push("Client certificate is missing");
    }
    
    if (!config.key) {
      errors.push("Client private key is missing");
    }
    
    // 验证证书和私钥是否匹配
    if (config.cert && config.key) {
      try {
        const crypto = require('crypto');
        
        // 简单的证书-私钥匹配验证
        const cert = crypto.createPublicKey(config.cert);
        const key = crypto.createPrivateKey({
          key: config.key,
          passphrase: config.passphrase
        });
        
        // 检查公钥是否匹配
        const certPubKey = cert.export({ type: 'spki', format: 'der' });
        const keyPubKey = crypto.createPublicKey(key).export({ type: 'spki', format: 'der' });
        
        if (!certPubKey.equals(keyPubKey)) {
          errors.push("Certificate and private key do not match");
        }
      } catch (error) {
        errors.push(`Certificate validation failed: ${error.message}`);
      }
    }
    
    return {
      valid: errors.length === 0,
      errors,
      message: errors.length === 0 ? "mTLS configuration is valid" : "mTLS configuration has errors"
    };
  }

  /**
   * 获取配置状态
   * @returns {Object} 配置状态
   */
  getStatus() {
    const config = this.getConfig();
    const validation = this.validateConfig();
    
    return {
      enabled: this.isEnabled(),
      hasCert: !!(config?.cert),
      hasKey: !!(config?.key),
      hasPassphrase: !!(config?.passphrase),
      validation,
      environmentVars: {
        CLAUDE_CODE_CLIENT_CERT: !!process.env.CLAUDE_CODE_CLIENT_CERT,
        CLAUDE_CODE_CLIENT_KEY: !!process.env.CLAUDE_CODE_CLIENT_KEY,
        CLAUDE_CODE_CLIENT_KEY_PASSPHRASE: !!process.env.CLAUDE_CODE_CLIENT_KEY_PASSPHRASE,
        NODE_EXTRA_CA_CERTS: !!process.env.NODE_EXTRA_CA_CERTS
      }
    };
  }

  /**
   * 清除缓存
   */
  clearCache() {
    this.configCache = null;
    this.agentCache.clear();
    
    // 清除懒加载器缓存
    if (getMTLSConfig.cache) {
      getMTLSConfig.cache.clear();
    }
    
    if (createHTTPSAgent.cache) {
      createHTTPSAgent.cache.clear();
    }
    
    if (createPoolAgent.cache) {
      createPoolAgent.cache.clear();
    }
  }

  /**
   * 重新加载配置
   */
  reload() {
    this.clearCache();
    return this.getConfig();
  }
}

// 辅助函数

/**
 * 创建懒加载器
 * @param {Function} fn - 要懒加载的函数
 * @returns {Function} 懒加载函数
 */
function createLazyLoader(fn) {
  let cached = null;
  let hasValue = false;
  
  const loader = (...args) => {
    if (!hasValue) {
      cached = fn(...args);
      hasValue = true;
    }
    return cached;
  };
  
  // 添加缓存控制
  loader.cache = {
    clear: () => {
      cached = null;
      hasValue = false;
    }
  };
  
  return loader;
}

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 */
function getFileSystem() {
  return require('fs');
}

/**
 * 记录信息日志
 * @param {string} message - 日志消息
 */
function logInfo(message) {
  console.log(message);
}

/**
 * 记录错误日志
 * @param {string} message - 错误消息
 */
function logError(message) {
  console.error(message);
}

// 创建默认mTLS服务实例
export const mtlsService = new MTLSService();

// 导出便捷函数
export const mTLS = {
  isEnabled: () => mtlsService.isEnabled(),
  getConfig: () => mtlsService.getConfig(),
  getHTTPSAgent: () => mtlsService.getHTTPSAgent(),
  getCertConfig: () => mtlsService.getCertConfig(),
  createHTTPConfig: (config) => mtlsService.createHTTPConfig(config),
  createAxiosConfig: (config) => mtlsService.createAxiosConfig(config),
  createFetchConfig: (config) => mtlsService.createFetchConfig(config),
  validateConfig: () => mtlsService.validateConfig(),
  getStatus: () => mtlsService.getStatus()
};
