/**
 * 会话管理服务
 * @description 重构自原始文件中的会话管理函数，对应第2002-2050行
 * @original 原始代码行 2002-2050
 */

/**
 * 会话状态对象
 * @description 存储会话的各种状态信息
 * @original i2对象的实现
 */
const sessionState = {
  sessionId: null,
  cwd: null,
  startTime: Date.now(),
  lastInteractionTime: Date.now(),
  totalCostUSD: 0,
  totalAPIDuration: 0,
  totalAPIDurationWithoutRetries: 0,
  totalLinesAdded: 0,
  totalLinesRemoved: 0,
  modelUsage: {},
  hasUnknownModelCost: false,
  initialMainLoopModel: null,
  mainLoopModelOverride: null,
  maxRateLimitFallbackActive: false,
  // 指标收集器
  meter: null,
  sessionCounter: null,
  locCounter: null,
  prCounter: null,
  commitCounter: null,
  costCounter: null,
  tokenCounter: null,
  codeEditToolDecisionCounter: null,
  activeTimeCounter: null,
  // 日志和事件
  loggerProvider: null,
  eventLogger: null,
  // 会话配置
  isNonInteractiveSession: false,
  isInteractive: true,
  clientType: null,
  agentColorMap: new Map(),
  agentColorIndex: 0,
  flagSettingsPath: null,
  // 后台Shell管理
  backgroundShells: new Map(),
  backgroundShellSubscribers: new Set(),
  backgroundShellCounter: 0
};

/**
 * 生成新的会话ID
 * @returns {string} 新的会话ID
 * @original function Mj0() { return i2.sessionId = Lj0(), i2.sessionId; }
 */
export function generateSessionId() {
  sessionState.sessionId = createUniqueId();
  return sessionState.sessionId;
}

/**
 * 设置会话ID
 * @param {string} sessionId - 会话ID
 * @original function Rj0(A) { i2.sessionId = A; }
 */
export function setSessionId(sessionId) {
  sessionState.sessionId = sessionId;
}

/**
 * 获取当前会话ID
 * @returns {string} 当前会话ID
 */
export function getSessionId() {
  return sessionState.sessionId;
}

/**
 * 设置当前工作目录
 * @param {string} cwd - 工作目录路径
 * @original function Tj0(A) { i2.cwd = A; }
 */
export function setCurrentWorkingDirectory(cwd) {
  sessionState.cwd = cwd;
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 */
export function getCurrentWorkingDirectory() {
  return sessionState.cwd;
}

/**
 * 记录API使用情况
 * @param {number} costUSD - 成本（美元）
 * @param {number} apiDuration - API持续时间
 * @param {number} apiDurationWithoutRetries - 不包含重试的API持续时间
 * @param {Object} tokenUsage - 令牌使用情况
 * @param {string} modelName - 模型名称
 * @original async function Pj0(A, B, Q, D, Z) { ... }
 */
export async function recordApiUsage(costUSD, apiDuration, apiDurationWithoutRetries, tokenUsage, modelName) {
  // 更新总计数据
  sessionState.totalCostUSD += costUSD;
  sessionState.totalAPIDuration += apiDuration;
  sessionState.totalAPIDurationWithoutRetries += apiDurationWithoutRetries;
  
  // 获取或创建模型使用记录
  let modelUsage = sessionState.modelUsage[modelName] ?? {
    inputTokens: 0,
    outputTokens: 0,
    cacheReadInputTokens: 0,
    cacheCreationInputTokens: 0,
    webSearchRequests: 0
  };
  
  // 更新模型使用数据
  modelUsage.inputTokens += tokenUsage.input_tokens;
  modelUsage.outputTokens += tokenUsage.output_tokens;
  modelUsage.cacheReadInputTokens += tokenUsage.cache_read_input_tokens ?? 0;
  modelUsage.cacheCreationInputTokens += tokenUsage.cache_creation_input_tokens ?? 0;
  modelUsage.webSearchRequests += tokenUsage.server_tool_use?.web_search_requests ?? 0;
  
  // 保存更新后的模型使用记录
  sessionState.modelUsage[modelName] = modelUsage;
}

/**
 * 获取总成本
 * @returns {number} 总成本（美元）
 * @original function aq() { return i2.totalCostUSD; }
 */
export function getTotalCost() {
  return sessionState.totalCostUSD;
}

/**
 * 获取总API持续时间
 * @returns {number} 总API持续时间
 * @original function fj() { return i2.totalAPIDuration; }
 */
export function getTotalApiDuration() {
  return sessionState.totalAPIDuration;
}

/**
 * 获取会话持续时间
 * @returns {number} 会话持续时间（毫秒）
 * @original function uu1() { return Date.now() - i2.startTime; }
 */
export function getSessionDuration() {
  return Date.now() - sessionState.startTime;
}

/**
 * 更新最后交互时间
 * @original function nA1() { i2.lastInteractionTime = Date.now(); }
 */
export function updateLastInteractionTime() {
  sessionState.lastInteractionTime = Date.now();
}

/**
 * 获取最后交互时间
 * @returns {number} 最后交互时间戳
 */
export function getLastInteractionTime() {
  return sessionState.lastInteractionTime;
}

/**
 * 记录代码行数变化
 * @param {number} linesAdded - 添加的行数
 * @param {number} linesRemoved - 删除的行数
 * @original function mu1(A, B) { i2.totalLinesAdded += A, i2.totalLinesRemoved += B; }
 */
export function recordLinesChanged(linesAdded, linesRemoved) {
  sessionState.totalLinesAdded += linesAdded;
  sessionState.totalLinesRemoved += linesRemoved;
}

/**
 * 获取总添加行数
 * @returns {number} 总添加行数
 * @original function JW1() { return i2.totalLinesAdded; }
 */
export function getTotalLinesAdded() {
  return sessionState.totalLinesAdded;
}

/**
 * 获取总删除行数
 * @returns {number} 总删除行数
 * @original function XW1() { return i2.totalLinesRemoved; }
 */
export function getTotalLinesRemoved() {
  return sessionState.totalLinesRemoved;
}

/**
 * 获取总输入令牌数
 * @returns {number} 总输入令牌数
 * @original function Sj0() { return Qf(Object.values(i2.modelUsage), "inputTokens"); }
 */
export function getTotalInputTokens() {
  return sumBy(Object.values(sessionState.modelUsage), "inputTokens");
}

/**
 * 获取总输出令牌数
 * @returns {number} 总输出令牌数
 * @original function jj0() { return Qf(Object.values(i2.modelUsage), "outputTokens"); }
 */
export function getTotalOutputTokens() {
  return sumBy(Object.values(sessionState.modelUsage), "outputTokens");
}

/**
 * 获取总缓存读取输入令牌数
 * @returns {number} 总缓存读取输入令牌数
 * @original function yj0() { return Qf(Object.values(i2.modelUsage), "cacheReadInputTokens"); }
 */
export function getTotalCacheReadInputTokens() {
  return sumBy(Object.values(sessionState.modelUsage), "cacheReadInputTokens");
}

/**
 * 获取总缓存创建输入令牌数
 * @returns {number} 总缓存创建输入令牌数
 */
export function getTotalCacheCreationInputTokens() {
  return sumBy(Object.values(sessionState.modelUsage), "cacheCreationInputTokens");
}

/**
 * 获取总Web搜索请求数
 * @returns {number} 总Web搜索请求数
 */
export function getTotalWebSearchRequests() {
  return sumBy(Object.values(sessionState.modelUsage), "webSearchRequests");
}

/**
 * 获取模型使用统计
 * @param {string} modelName - 模型名称
 * @returns {Object} 模型使用统计
 */
export function getModelUsage(modelName) {
  return sessionState.modelUsage[modelName] || {
    inputTokens: 0,
    outputTokens: 0,
    cacheReadInputTokens: 0,
    cacheCreationInputTokens: 0,
    webSearchRequests: 0
  };
}

/**
 * 获取所有模型使用统计
 * @returns {Object} 所有模型使用统计
 */
export function getAllModelUsage() {
  return { ...sessionState.modelUsage };
}

/**
 * 重置会话状态
 */
export function resetSession() {
  sessionState.sessionId = null;
  sessionState.cwd = null;
  sessionState.startTime = Date.now();
  sessionState.lastInteractionTime = Date.now();
  sessionState.totalCostUSD = 0;
  sessionState.totalAPIDuration = 0;
  sessionState.totalAPIDurationWithoutRetries = 0;
  sessionState.totalLinesAdded = 0;
  sessionState.totalLinesRemoved = 0;
  sessionState.modelUsage = {};
}

/**
 * 获取会话摘要
 * @returns {Object} 会话摘要信息
 */
export function getSessionSummary() {
  return {
    sessionId: sessionState.sessionId,
    cwd: sessionState.cwd,
    startTime: sessionState.startTime,
    lastInteractionTime: sessionState.lastInteractionTime,
    sessionDuration: getSessionDuration(),
    totalCostUSD: sessionState.totalCostUSD,
    totalAPIDuration: sessionState.totalAPIDuration,
    totalAPIDurationWithoutRetries: sessionState.totalAPIDurationWithoutRetries,
    totalLinesAdded: sessionState.totalLinesAdded,
    totalLinesRemoved: sessionState.totalLinesRemoved,
    totalInputTokens: getTotalInputTokens(),
    totalOutputTokens: getTotalOutputTokens(),
    totalCacheReadInputTokens: getTotalCacheReadInputTokens(),
    totalCacheCreationInputTokens: getTotalCacheCreationInputTokens(),
    totalWebSearchRequests: getTotalWebSearchRequests(),
    modelUsage: getAllModelUsage()
  };
}

/**
 * 导出会话数据
 * @returns {string} JSON格式的会话数据
 */
export function exportSessionData() {
  return JSON.stringify(getSessionSummary(), null, 2);
}

/**
 * 导入会话数据
 * @param {string} jsonData - JSON格式的会话数据
 * @returns {boolean} 是否导入成功
 */
export function importSessionData(jsonData) {
  try {
    const data = JSON.parse(jsonData);
    
    // 验证数据格式
    if (!data || typeof data !== 'object') {
      return false;
    }
    
    // 恢复会话状态
    Object.assign(sessionState, {
      sessionId: data.sessionId || null,
      cwd: data.cwd || null,
      startTime: data.startTime || Date.now(),
      lastInteractionTime: data.lastInteractionTime || Date.now(),
      totalCostUSD: data.totalCostUSD || 0,
      totalAPIDuration: data.totalAPIDuration || 0,
      totalAPIDurationWithoutRetries: data.totalAPIDurationWithoutRetries || 0,
      totalLinesAdded: data.totalLinesAdded || 0,
      totalLinesRemoved: data.totalLinesRemoved || 0,
      modelUsage: data.modelUsage || {}
    });
    
    return true;
  } catch (error) {
    console.error('Failed to import session data:', error);
    return false;
  }
}

// 辅助函数

/**
 * 创建唯一ID
 * @returns {string} 唯一ID
 * @original Lj0()函数的实现
 */
function createUniqueId() {
  // 简单的UUID v4实现
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

/**
 * 获取总缓存创建输入令牌数
 * @returns {number} 总缓存创建输入令牌数
 * @original function kj0() { return Qf(Object.values(i2.modelUsage), "cacheCreationInputTokens"); }
 */
export function getTotalCacheCreationInputTokensAlt() {
  return sumBy(Object.values(sessionState.modelUsage), "cacheCreationInputTokens");
}

/**
 * 获取总Web搜索请求数
 * @returns {number} 总Web搜索请求数
 * @original function _j0() { return Qf(Object.values(i2.modelUsage), "webSearchRequests"); }
 */
export function getTotalWebSearchRequestsAlt() {
  return sumBy(Object.values(sessionState.modelUsage), "webSearchRequests");
}

/**
 * 标记存在未知模型成本
 * @original function du1() { i2.hasUnknownModelCost = !0; }
 */
export function markHasUnknownModelCost() {
  sessionState.hasUnknownModelCost = true;
}

/**
 * 检查是否存在未知模型成本
 * @returns {boolean} 是否存在未知模型成本
 * @original function xj0() { return i2.hasUnknownModelCost; }
 */
export function hasUnknownModelCost() {
  return sessionState.hasUnknownModelCost;
}

/**
 * 获取最后交互时间（别名）
 * @returns {number} 最后交互时间戳
 * @original function VW1() { return i2.lastInteractionTime; }
 */
export function getLastInteractionTimeAlt() {
  return sessionState.lastInteractionTime;
}

/**
 * 获取模型使用情况（别名）
 * @returns {Object} 模型使用情况
 * @original function vj0() { return i2.modelUsage; }
 */
export function getModelUsageAlt() {
  return sessionState.modelUsage;
}

/**
 * 获取初始主循环模型
 * @returns {string} 初始主循环模型
 * @original function CW1() { return i2.initialMainLoopModel; }
 */
export function getInitialMainLoopModel() {
  return sessionState.initialMainLoopModel;
}

/**
 * 设置主循环模型覆盖
 * @param {string} model - 模型名称
 * @original function sA1(A) { i2.mainLoopModelOverride = A; }
 */
export function setMainLoopModelOverride(model) {
  sessionState.mainLoopModelOverride = model;
}

/**
 * 获取主循环模型覆盖
 * @returns {string} 主循环模型覆盖
 */
export function getMainLoopModelOverride() {
  return sessionState.mainLoopModelOverride;
}

/**
 * 设置最大速率限制回退状态
 * @param {boolean} active - 是否激活
 * @original function bj0(A) { i2.maxRateLimitFallbackActive = A; }
 */
export function setMaxRateLimitFallbackActive(active) {
  sessionState.maxRateLimitFallbackActive = active;
}

/**
 * 获取最大速率限制回退状态
 * @returns {boolean} 是否激活
 */
export function getMaxRateLimitFallbackActive() {
  return sessionState.maxRateLimitFallbackActive;
}

/**
 * 设置初始主循环模型
 * @param {string} model - 模型名称
 * @original function fj0(A) { i2.initialMainLoopModel = A; }
 */
export function setInitialMainLoopModel(model) {
  sessionState.initialMainLoopModel = model;
}

/**
 * 初始化指标收集器
 * @param {Object} meter - 指标收集器
 * @param {Function} createCounter - 创建计数器函数
 * @original function hj0(A, B) { ... }
 */
export function initializeMetrics(meter, createCounter) {
  sessionState.meter = meter;

  sessionState.sessionCounter = createCounter("claude_code.session.count", {
    description: "Count of CLI sessions started"
  });

  sessionState.locCounter = createCounter("claude_code.lines_of_code.count", {
    description: "Count of lines of code modified, with the 'type' attribute indicating whether lines were added or removed"
  });

  sessionState.prCounter = createCounter("claude_code.pull_request.count", {
    description: "Number of pull requests created"
  });

  sessionState.commitCounter = createCounter("claude_code.commit.count", {
    description: "Number of git commits created"
  });

  sessionState.costCounter = createCounter("claude_code.cost.usage", {
    description: "Cost of the Claude Code session",
    unit: "USD"
  });

  sessionState.tokenCounter = createCounter("claude_code.token.usage", {
    description: "Number of tokens used",
    unit: "tokens"
  });

  sessionState.codeEditToolDecisionCounter = createCounter("claude_code.code_edit_tool.decision", {
    description: "Count of code editing tool permission decisions (accept/reject) for Edit, MultiEdit, Write, and NotebookEdit tools"
  });

  sessionState.activeTimeCounter = createCounter("claude_code.active_time.total", {
    description: "Total active time in seconds",
    unit: "s"
  });
}

/**
 * 获取会话计数器
 * @returns {Object} 会话计数器
 * @original function gj0() { return i2.sessionCounter; }
 */
export function getSessionCounter() {
  return sessionState.sessionCounter;
}

/**
 * 获取代码行计数器
 * @returns {Object} 代码行计数器
 * @original function lu1() { return i2.locCounter; }
 */
export function getLinesOfCodeCounter() {
  return sessionState.locCounter;
}

/**
 * 获取PR计数器
 * @returns {Object} PR计数器
 * @original function uj0() { return i2.prCounter; }
 */
export function getPullRequestCounter() {
  return sessionState.prCounter;
}

/**
 * 获取提交计数器
 * @returns {Object} 提交计数器
 * @original function mj0() { return i2.commitCounter; }
 */
export function getCommitCounter() {
  return sessionState.commitCounter;
}

/**
 * 获取成本计数器
 * @returns {Object} 成本计数器
 * @original function dj0() { return i2.costCounter; }
 */
export function getCostCounter() {
  return sessionState.costCounter;
}

/**
 * 获取令牌计数器
 * @returns {Object} 令牌计数器
 * @original function rA1() { return i2.tokenCounter; }
 */
export function getTokenCounter() {
  return sessionState.tokenCounter;
}

/**
 * 获取代码编辑工具决策计数器
 * @returns {Object} 代码编辑工具决策计数器
 * @original function ac() { return i2.codeEditToolDecisionCounter; }
 */
export function getCodeEditToolDecisionCounter() {
  return sessionState.codeEditToolDecisionCounter;
}

/**
 * 获取活跃时间计数器
 * @returns {Object} 活跃时间计数器
 * @original function pu1() { return i2.activeTimeCounter; }
 */
export function getActiveTimeCounter() {
  return sessionState.activeTimeCounter;
}

/**
 * 获取日志提供者
 * @returns {Object} 日志提供者
 * @original function cj0() { return i2.loggerProvider; }
 */
export function getLoggerProvider() {
  return sessionState.loggerProvider;
}

/**
 * 设置日志提供者
 * @param {Object} loggerProvider - 日志提供者
 * @original function lj0(A) { i2.loggerProvider = A; }
 */
export function setLoggerProvider(loggerProvider) {
  sessionState.loggerProvider = loggerProvider;
}

/**
 * 获取事件日志记录器
 * @returns {Object} 事件日志记录器
 * @original function pj0() { return i2.eventLogger; }
 */
export function getEventLogger() {
  return sessionState.eventLogger;
}

/**
 * 设置事件日志记录器
 * @param {Object} eventLogger - 事件日志记录器
 * @original function ij0(A) { i2.eventLogger = A; }
 */
export function setEventLogger(eventLogger) {
  sessionState.eventLogger = eventLogger;
}

/**
 * 设置非交互式会话状态
 * @param {boolean} isNonInteractive - 是否为非交互式会话
 * @original function nj0(A) { i2.isNonInteractiveSession = A; }
 */
export function setNonInteractiveSession(isNonInteractive) {
  sessionState.isNonInteractiveSession = isNonInteractive;
}

/**
 * 获取非交互式会话状态
 * @returns {boolean} 是否为非交互式会话
 */
export function isNonInteractiveSession() {
  return sessionState.isNonInteractiveSession;
}

/**
 * 设置交互式状态
 * @param {boolean} isInteractive - 是否为交互式
 * @original function sj0(A) { i2.isInteractive = A; }
 */
export function setInteractive(isInteractive) {
  sessionState.isInteractive = isInteractive;
}

/**
 * 获取交互式状态
 * @returns {boolean} 是否为交互式
 */
export function isInteractive() {
  return sessionState.isInteractive;
}

/**
 * 设置客户端类型
 * @param {string} clientType - 客户端类型
 * @original function oj0(A) { i2.clientType = A; }
 */
export function setClientType(clientType) {
  sessionState.clientType = clientType;
}

/**
 * 获取客户端类型
 * @returns {string} 客户端类型
 */
export function getClientType() {
  return sessionState.clientType;
}

/**
 * 获取代理颜色映射
 * @returns {Map} 代理颜色映射
 * @original function iu1() { return i2.agentColorMap; }
 */
export function getAgentColorMap() {
  return sessionState.agentColorMap;
}

/**
 * 设置代理颜色
 * @param {string} agentId - 代理ID
 * @param {string} color - 颜色
 */
export function setAgentColor(agentId, color) {
  sessionState.agentColorMap.set(agentId, color);
}

/**
 * 获取代理颜色
 * @param {string} agentId - 代理ID
 * @returns {string} 颜色
 */
export function getAgentColor(agentId) {
  return sessionState.agentColorMap.get(agentId);
}

/**
 * 清除代理颜色映射
 */
export function clearAgentColorMap() {
  sessionState.agentColorMap.clear();
}

/**
 * 获取代理颜色索引
 * @returns {number} 代理颜色索引
 * @original function tj0() { return i2.agentColorIndex; }
 */
export function getAgentColorIndex() {
  return sessionState.agentColorIndex;
}

/**
 * 递增代理颜色索引
 * @original function ej0() { i2.agentColorIndex++; }
 */
export function incrementAgentColorIndex() {
  sessionState.agentColorIndex++;
}

/**
 * 设置标志设置路径
 * @param {string} path - 标志设置路径
 * @original function Ay0(A) { i2.flagSettingsPath = A; }
 */
export function setFlagSettingsPath(path) {
  sessionState.flagSettingsPath = path;
}

/**
 * 获取标志设置路径
 * @returns {string} 标志设置路径
 */
export function getFlagSettingsPath() {
  return sessionState.flagSettingsPath;
}

/**
 * 获取后台Shell映射
 * @returns {Map} 后台Shell映射
 * @original function sq() { return i2.backgroundShells; }
 */
export function getBackgroundShells() {
  return sessionState.backgroundShells;
}

/**
 * 设置后台Shell
 * @param {string} id - Shell ID
 * @param {Object} shell - Shell对象
 * @original function By0(A, B) { i2.backgroundShells.set(A, B); }
 */
export function setBackgroundShell(id, shell) {
  sessionState.backgroundShells.set(id, shell);
}

/**
 * 删除后台Shell
 * @param {string} id - Shell ID
 * @returns {boolean} 是否删除成功
 * @original function Qy0(A) { return i2.backgroundShells.delete(A); }
 */
export function deleteBackgroundShell(id) {
  return sessionState.backgroundShells.delete(id);
}

/**
 * 获取后台Shell订阅者
 * @returns {Set} 后台Shell订阅者集合
 * @original function rc() { return i2.backgroundShellSubscribers; }
 */
export function getBackgroundShellSubscribers() {
  return sessionState.backgroundShellSubscribers;
}

/**
 * 获取下一个后台Shell计数器值
 * @returns {number} 下一个计数器值
 * @original function Dy0() { return ++i2.backgroundShellCounter; }
 */
export function getNextBackgroundShellId() {
  return ++sessionState.backgroundShellCounter;
}

/**
 * 添加后台Shell订阅者
 * @param {Object} subscriber - 订阅者对象
 */
export function addBackgroundShellSubscriber(subscriber) {
  sessionState.backgroundShellSubscribers.add(subscriber);
}

/**
 * 移除后台Shell订阅者
 * @param {Object} subscriber - 订阅者对象
 * @returns {boolean} 是否移除成功
 */
export function removeBackgroundShellSubscriber(subscriber) {
  return sessionState.backgroundShellSubscribers.delete(subscriber);
}

/**
 * 清除所有后台Shell
 */
export function clearBackgroundShells() {
  sessionState.backgroundShells.clear();
}

/**
 * 清除所有后台Shell订阅者
 */
export function clearBackgroundShellSubscribers() {
  sessionState.backgroundShellSubscribers.clear();
}

/**
 * 获取后台Shell数量
 * @returns {number} 后台Shell数量
 */
export function getBackgroundShellCount() {
  return sessionState.backgroundShells.size;
}

/**
 * 检查是否存在指定的后台Shell
 * @param {string} id - Shell ID
 * @returns {boolean} 是否存在
 */
export function hasBackgroundShell(id) {
  return sessionState.backgroundShells.has(id);
}

/**
 * 获取指定的后台Shell
 * @param {string} id - Shell ID
 * @returns {Object} Shell对象
 */
export function getBackgroundShell(id) {
  return sessionState.backgroundShells.get(id);
}

/**
 * 获取所有后台Shell的ID列表
 * @returns {Array} Shell ID数组
 */
export function getBackgroundShellIds() {
  return Array.from(sessionState.backgroundShells.keys());
}

/**
 * 获取所有后台Shell对象列表
 * @returns {Array} Shell对象数组
 */
export function getBackgroundShellList() {
  return Array.from(sessionState.backgroundShells.values());
}

/**
 * 遍历所有后台Shell
 * @param {Function} callback - 回调函数 (shell, id) => void
 */
export function forEachBackgroundShell(callback) {
  sessionState.backgroundShells.forEach((shell, id) => {
    callback(shell, id);
  });
}

/**
 * 通知所有后台Shell订阅者
 * @param {string} event - 事件名称
 * @param {*} data - 事件数据
 */
export function notifyBackgroundShellSubscribers(event, data) {
  sessionState.backgroundShellSubscribers.forEach(subscriber => {
    if (typeof subscriber.onEvent === 'function') {
      try {
        subscriber.onEvent(event, data);
      } catch (error) {
        console.error('Error notifying background shell subscriber:', error);
      }
    }
  });
}

/**
 * 对数组中的对象按指定属性求和
 * @param {Array} array - 对象数组
 * @param {string} property - 属性名
 * @returns {number} 求和结果
 * @original Qf函数的实现
 */
function sumBy(array, property) {
  let result = 0;

  for (const item of array) {
    if (item && typeof item === 'object' && property in item) {
      const value = item[property];
      if (typeof value === 'number' && !isNaN(value)) {
        result += value;
      }
    }
  }

  return result;
}
