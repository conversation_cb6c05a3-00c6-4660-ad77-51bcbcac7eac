/**
 * 文件缓存服务
 * @description 重构自原始文件中的文件缓存实现，对应第2789-2800行
 * @original 原始代码行 2789-2800
 */

import { homedir } from "os";

/**
 * 文件缓存服务类
 * @description 提供文件内容缓存功能，基于文件修改时间进行缓存失效
 * @original nFA类
 */
export class FileCacheService {
  constructor(maxCacheSize = 1000) {
    this.cache = new Map();
    this.maxCacheSize = maxCacheSize;
  }

  /**
   * 读取文件内容（带缓存）
   * @param {string} filePath - 文件路径
   * @returns {Object} 包含content和encoding的对象
   * @throws {Error} 文件读取错误
   * @original readFile方法的完整实现
   */
  readFile(filePath) {
    const fs = getFileSystem();
    let stats;

    try {
      stats = fs.statSync(filePath);
    } catch (error) {
      // 如果文件不存在或无法访问，从缓存中删除并抛出错误
      this.cache.delete(filePath);
      throw error;
    }

    const cacheKey = filePath;
    const cachedEntry = this.cache.get(cacheKey);

    // 检查缓存是否有效（使用mtimeMs进行比较）
    if (cachedEntry && cachedEntry.mtime === stats.mtimeMs) {
      return {
        content: cachedEntry.content,
        encoding: cachedEntry.encoding
      };
    }

    // 检测文件编码
    const encoding = detectFileEncoding(filePath);

    // 读取文件内容
    let content;
    try {
      content = fs.readFileSync(filePath, { encoding }).replaceAll('\r\n', '\n');
    } catch (error) {
      this.cache.delete(cacheKey);
      throw error;
    }

    // 更新缓存
    this.cache.set(cacheKey, {
      content,
      encoding,
      mtime: stats.mtimeMs
    });

    // 如果缓存超过最大大小，删除最旧的条目
    if (this.cache.size > this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    return {
      content,
      encoding
    };
  }

  /**
   * 异步读取文件内容（带缓存）
   * @param {string} filePath - 文件路径
   * @returns {Promise<string>} 文件内容
   */
  async readFileAsync(filePath) {
    const fs = getFileSystem();
    let stats;
    
    try {
      stats = await fs.promises.stat(filePath);
    } catch (error) {
      this.cache.delete(filePath);
      throw error;
    }
    
    const cacheKey = filePath;
    const cachedEntry = this.cache.get(cacheKey);
    
    // 检查缓存是否有效
    if (cachedEntry && this.isCacheValid(cachedEntry, stats)) {
      return cachedEntry.content;
    }
    
    // 读取文件内容
    let content;
    try {
      content = await fs.promises.readFile(filePath, 'utf8');
    } catch (error) {
      this.cache.delete(cacheKey);
      throw error;
    }
    
    // 更新缓存
    this.updateCache(cacheKey, content, stats);
    
    return content;
  }

  /**
   * 检查缓存是否有效
   * @param {Object} cachedEntry - 缓存条目
   * @param {Object} currentStats - 当前文件统计信息
   * @returns {boolean} 缓存是否有效
   * @private
   */
  isCacheValid(cachedEntry, currentStats) {
    return cachedEntry.mtime.getTime() === currentStats.mtime.getTime() &&
           cachedEntry.size === currentStats.size;
  }

  /**
   * 更新缓存
   * @param {string} cacheKey - 缓存键
   * @param {string} content - 文件内容
   * @param {Object} stats - 文件统计信息
   * @private
   */
  updateCache(cacheKey, content, stats) {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    // 添加新的缓存条目
    this.cache.set(cacheKey, {
      content,
      mtime: stats.mtime,
      size: stats.size,
      cachedAt: new Date()
    });
  }

  /**
   * 清除指定文件的缓存
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否成功清除
   */
  clearCache(filePath) {
    return this.cache.delete(filePath);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    const entries = Array.from(this.cache.entries());
    const totalSize = entries.reduce((sum, [, entry]) => sum + entry.content.length, 0);
    
    return {
      entryCount: this.cache.size,
      maxSize: this.maxCacheSize,
      totalContentSize: totalSize,
      averageContentSize: entries.length > 0 ? Math.round(totalSize / entries.length) : 0,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(([, entry]) => entry.cachedAt.getTime())) : null,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(([, entry]) => entry.cachedAt.getTime())) : null
    };
  }

  /**
   * 获取缓存中的所有文件路径
   * @returns {string[]} 文件路径数组
   */
  getCachedFiles() {
    return Array.from(this.cache.keys());
  }

  /**
   * 检查文件是否在缓存中
   * @param {string} filePath - 文件路径
   * @returns {boolean} 是否在缓存中
   */
  isCached(filePath) {
    return this.cache.has(filePath);
  }

  /**
   * 获取缓存条目信息
   * @param {string} filePath - 文件路径
   * @returns {Object|null} 缓存条目信息或null
   */
  getCacheInfo(filePath) {
    const entry = this.cache.get(filePath);
    if (!entry) return null;
    
    return {
      filePath,
      contentLength: entry.content.length,
      mtime: entry.mtime,
      size: entry.size,
      cachedAt: entry.cachedAt,
      age: Date.now() - entry.cachedAt.getTime()
    };
  }

  /**
   * 预加载文件到缓存
   * @param {string[]} filePaths - 文件路径数组
   * @returns {Promise<Object>} 预加载结果
   */
  async preloadFiles(filePaths) {
    const results = {
      success: [],
      failed: []
    };
    
    for (const filePath of filePaths) {
      try {
        await this.readFileAsync(filePath);
        results.success.push(filePath);
      } catch (error) {
        results.failed.push({ filePath, error: error.message });
      }
    }
    
    return results;
  }

  /**
   * 清理过期缓存
   * @param {number} maxAge - 最大缓存时间（毫秒）
   * @returns {number} 清理的条目数量
   */
  cleanupExpiredCache(maxAge = 24 * 60 * 60 * 1000) { // 默认24小时
    const now = Date.now();
    let cleanedCount = 0;
    
    for (const [filePath, entry] of this.cache.entries()) {
      if (now - entry.cachedAt.getTime() > maxAge) {
        this.cache.delete(filePath);
        cleanedCount++;
      }
    }
    
    return cleanedCount;
  }

  /**
   * 设置最大缓存大小
   * @param {number} maxSize - 最大缓存大小
   */
  setMaxCacheSize(maxSize) {
    this.maxCacheSize = maxSize;
    
    // 如果当前缓存超过新的最大值，清理多余的条目
    while (this.cache.size > maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
  }

  /**
   * 获取最大缓存大小
   * @returns {number} 最大缓存大小
   */
  getMaxCacheSize() {
    return this.maxCacheSize;
  }

  /**
   * 刷新指定文件的缓存
   * @param {string} filePath - 文件路径
   * @returns {Promise<string>} 刷新后的文件内容
   */
  async refreshCache(filePath) {
    this.cache.delete(filePath);
    return this.readFileAsync(filePath);
  }

  /**
   * 批量刷新缓存
   * @param {string[]} filePaths - 文件路径数组
   * @returns {Promise<Object>} 刷新结果
   */
  async refreshMultipleCache(filePaths) {
    const results = {
      success: [],
      failed: []
    };
    
    for (const filePath of filePaths) {
      try {
        await this.refreshCache(filePath);
        results.success.push(filePath);
      } catch (error) {
        results.failed.push({ filePath, error: error.message });
      }
    }
    
    return results;
  }
}

/**
 * 检测文件编码
 * @param {string} filePath - 文件路径
 * @returns {string} 文件编码
 * @original UY函数的实现
 */
function detectFileEncoding(filePath) {
  // @todo: 实现更完整的编码检测逻辑
  // 这里返回默认的utf8编码
  return 'utf8';
}

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 * @original x1()函数的实现（推测）
 */
function getFileSystem() {
  // @todo: 实现x1函数的逻辑，可能是动态导入fs模块
  return require('fs');
}

/**
 * 扩展的文件缓存服务
 * @description 提供更完整的文件缓存功能
 * @original 基于第2800-2837行的完整实现
 */
export class ExtendedFileCacheService extends FileCacheService {
  /**
   * 清除所有缓存
   * @original clear方法
   */
  clear() {
    this.cache.clear();
  }

  /**
   * 使指定文件的缓存失效
   * @param {string} filePath - 文件路径
   * @original invalidate方法
   */
  invalidate(filePath) {
    this.cache.delete(filePath);
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计信息
   * @original getStats方法
   */
  getStats() {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }

  /**
   * 读取文件内容（带编码检测）
   * @param {string} filePath - 文件路径
   * @returns {Object} 包含content和encoding的对象
   * @override
   */
  readFile(filePath) {
    const fs = getFileSystem();
    let stats;

    try {
      stats = fs.statSync(filePath);
    } catch (error) {
      this.cache.delete(filePath);
      throw error;
    }

    const cacheKey = filePath;
    const cachedEntry = this.cache.get(cacheKey);

    // 检查缓存是否有效（使用mtimeMs进行比较）
    if (cachedEntry && cachedEntry.mtime === stats.mtimeMs) {
      return {
        content: cachedEntry.content,
        encoding: cachedEntry.encoding
      };
    }

    // 检测文件编码
    const encoding = detectFileEncoding(filePath);

    // 读取文件内容并规范化换行符
    let content;
    try {
      content = fs.readFileSync(filePath, { encoding }).replaceAll('\r\n', '\n');
    } catch (error) {
      this.cache.delete(cacheKey);
      throw error;
    }

    // 更新缓存
    this.cache.set(cacheKey, {
      content,
      encoding,
      mtime: stats.mtimeMs
    });

    // 如果缓存超过最大大小，删除最旧的条目
    if (this.cache.size > this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    return {
      content,
      encoding
    };
  }
}

/**
 * 全局文件缓存实例
 */
export const globalFileCache = new FileCacheService();

/**
 * 创建文件缓存实例
 * @param {number} maxCacheSize - 最大缓存大小
 * @returns {FileCacheService} 文件缓存实例
 */
export function createFileCache(maxCacheSize = 1000) {
  return new FileCacheService(maxCacheSize);
}

/**
 * 文件缓存工具函数
 */
export const FileCacheUtils = {
  /**
   * 获取用户主目录
   * @returns {string} 用户主目录路径
   */
  getHomeDir() {
    return homedir();
  },

  /**
   * 创建缓存键
   * @param {string} filePath - 文件路径
   * @param {string} prefix - 前缀（可选）
   * @returns {string} 缓存键
   */
  createCacheKey(filePath, prefix = '') {
    return prefix ? `${prefix}:${filePath}` : filePath;
  },

  /**
   * 格式化文件大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小
   */
  formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(2)} ${units[unitIndex]}`;
  },

  /**
   * 格式化时间间隔
   * @param {number} milliseconds - 毫秒数
   * @returns {string} 格式化后的时间间隔
   */
  formatDuration(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `${days}d ${hours % 24}h`;
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }
};
