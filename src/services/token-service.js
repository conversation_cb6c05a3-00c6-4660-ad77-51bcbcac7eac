/**
 * 令牌管理服务
 * @description 重构自原始文件中的令牌管理代码，对应第5400-5500行
 * @original 原始代码行 5400-5500
 */

import { dirname } from "path";

/**
 * 检查API密钥是否已批准
 * @param {string} apiKey - API密钥
 * @returns {boolean} 是否已批准
 * @original BsA函数
 */
export function isAPIKeyApproved(apiKey) {
  const userConfig = getUserConfig();
  const keyHash = hashAPIKey(apiKey);
  return userConfig.customApiKeyResponses?.approved?.includes(keyHash) ?? false;
}

/**
 * 清除主API密钥
 * @original QsA函数
 */
export function clearPrimaryAPIKey() {
  // 清除Keychain中的密钥
  clearKeychainAPIKey();
  
  // 清除配置中的密钥
  const userConfig = getUserConfig();
  userConfig.primaryApiKey = undefined;
  saveUserConfig(userConfig);
  
  // 清除缓存
  clearAPIKeyCache();
}

/**
 * 从Keychain删除API密钥
 * @original DsA函数
 */
export function clearKeychainAPIKey() {
  if (process.platform === "darwin") {
    try {
      const serviceName = getKeychainServiceName();
      executeCommand(`security delete-generic-password -a $USER -s "${serviceName}"`);
    } catch (error) {
      logError(error);
    }
  }
}

/**
 * 存储OAuth令牌
 * @param {Object} tokenData - 令牌数据
 * @returns {Object} 存储结果
 * @original b41函数
 */
export function storeOAuthToken(tokenData) {
  // 如果作用域无效，直接返回成功
  if (!hasValidScopes(tokenData.scopes)) {
    return {
      success: true
    };
  }
  
  // 如果缺少必要的令牌信息，直接返回成功
  if (!tokenData.refreshToken || !tokenData.expiresAt) {
    return {
      success: true
    };
  }
  
  try {
    const configManager = getConfigManager();
    const currentConfig = configManager.read() || {};
    
    // 存储OAuth令牌信息
    currentConfig.claudeAiOauth = {
      accessToken: tokenData.accessToken,
      refreshToken: tokenData.refreshToken,
      expiresAt: tokenData.expiresAt,
      scopes: tokenData.scopes,
      subscriptionType: tokenData.subscriptionType
    };
    
    const result = configManager.update(currentConfig);
    
    // 清除相关缓存
    clearTokenDataCache();
    clearWorkspaceCache();
    
    return result;
  } catch (error) {
    logError(error);
    return {
      success: false,
      warning: "Failed to save OAuth tokens"
    };
  }
}

/**
 * 自动刷新令牌
 * @param {number} retryCount - 重试次数
 * @returns {Promise<boolean>} 是否刷新成功
 * @original ha函数
 */
export async function autoRefreshToken(retryCount = 0) {
  // 获取当前令牌数据
  let tokenData = getStoredTokenData();
  
  // 检查是否需要刷新
  if (!tokenData?.refreshToken || !isTokenExpiringSoon(tokenData.expiresAt)) {
    return false;
  }
  
  // 清除缓存并重新获取
  clearTokenDataCache();
  tokenData = getStoredTokenData();
  
  if (!tokenData?.refreshToken || !isTokenExpiringSoon(tokenData.expiresAt)) {
    return false;
  }
  
  // 获取锁目录
  const lockDir = getLockDirectory();
  const fs = getFileSystem();
  fs.mkdirSync(lockDir, { recursive: true });
  
  let releaseLock;
  
  try {
    // 尝试获取锁
    releaseLock = await acquireLock(lockDir);
  } catch (error) {
    if (error.code === "ELOCKED") {
      // 如果锁被占用且重试次数未达到上限，等待后重试
      if (retryCount < 5) {
        await new Promise(resolve => 
          setTimeout(resolve, 1000 + Math.random() * 1000)
        );
        return autoRefreshToken(retryCount + 1);
      }
      return false;
    }
    
    logError(error);
    return false;
  }
  
  try {
    // 再次检查令牌状态（防止在等待锁期间被其他进程刷新）
    clearTokenDataCache();
    tokenData = getStoredTokenData();
    
    if (!tokenData?.refreshToken || !isTokenExpiringSoon(tokenData.expiresAt)) {
      return false;
    }
    
    // 刷新令牌
    const { refreshAccessToken } = require('./oauth-service.js');
    const newTokenData = await refreshAccessToken(tokenData.refreshToken);
    
    // 存储新的令牌数据
    storeOAuthToken({
      ...newTokenData,
      scopes: tokenData.scopes
    });
    
    // 清除缓存
    clearTokenDataCache();
    
    return true;
  } catch (error) {
    logError(error instanceof Error ? error : new Error(String(error)));
    return false;
  } finally {
    // 释放锁
    if (releaseLock) {
      await releaseLock();
    }
  }
}

/**
 * 检查是否应该使用Claude AI
 * @returns {boolean} 是否应该使用Claude AI
 * @original ZsA函数
 */
export function shouldUseClaudeAI() {
  // 如果设置了使用Bedrock或Vertex，则不使用Claude AI
  if (process.env.CLAUDE_CODE_USE_BEDROCK === "true" || 
      process.env.CLAUDE_CODE_USE_VERTEX === "true") {
    return false;
  }
  
  // 如果是企业版本，则不使用Claude AI
  if (isEnterpriseVersion()) {
    return false;
  }
  
  return true;
}

/**
 * 获取当前用户类型
 * @returns {boolean} 是否为付费用户
 * @original GsA函数
 */
export function isPaidUser() {
  return hasValidSubscription();
}

/**
 * 获取订阅显示名称
 * @returns {string} 订阅显示名称
 * @original A$1函数
 */
export function getSubscriptionDisplayName() {
  const subscriptionType = getCurrentSubscriptionType();
  
  switch (subscriptionType) {
    case "enterprise":
      return "Claude Enterprise";
    case "team":
      return "Claude Team";
    case "max":
      return "Claude Max";
    case "pro":
      return "Claude Pro";
    default:
      return "Claude Free";
  }
}

/**
 * 令牌管理服务类
 * @description 提供完整的令牌管理功能
 */
export class TokenService {
  constructor() {
    this.refreshPromise = null;
    this.refreshInterval = null;
    this.autoRefreshEnabled = true;
  }

  /**
   * 获取有效的访问令牌
   * @param {boolean} autoRefresh - 是否自动刷新
   * @returns {Promise<string|null>} 访问令牌
   */
  async getValidAccessToken(autoRefresh = true) {
    const tokenData = getStoredTokenData();
    
    if (!tokenData?.accessToken) {
      return null;
    }
    
    // 如果令牌即将过期且启用自动刷新
    if (autoRefresh && this.autoRefreshEnabled && isTokenExpiringSoon(tokenData.expiresAt)) {
      const refreshed = await this.refreshTokenIfNeeded();
      if (refreshed) {
        const newTokenData = getStoredTokenData();
        return newTokenData?.accessToken || null;
      }
    }
    
    return tokenData.accessToken;
  }

  /**
   * 刷新令牌（如果需要）
   * @returns {Promise<boolean>} 是否刷新成功
   */
  async refreshTokenIfNeeded() {
    // 如果已经有刷新操作在进行，等待其完成
    if (this.refreshPromise) {
      return this.refreshPromise;
    }
    
    this.refreshPromise = autoRefreshToken();
    
    try {
      const result = await this.refreshPromise;
      return result;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * 存储令牌
   * @param {Object} tokenData - 令牌数据
   * @returns {Object} 存储结果
   */
  storeToken(tokenData) {
    return storeOAuthToken(tokenData);
  }

  /**
   * 清除所有令牌
   */
  clearAllTokens() {
    clearPrimaryAPIKey();
    this.clearOAuthTokens();
  }

  /**
   * 清除OAuth令牌
   */
  clearOAuthTokens() {
    const configManager = getConfigManager();
    const currentConfig = configManager.read() || {};
    
    delete currentConfig.claudeAiOauth;
    configManager.update(currentConfig);
    
    clearTokenDataCache();
    clearWorkspaceCache();
  }

  /**
   * 获取令牌状态
   * @returns {Object} 令牌状态
   */
  getTokenStatus() {
    const tokenData = getStoredTokenData();
    
    if (!tokenData) {
      return {
        hasToken: false,
        isValid: false,
        expiresAt: null,
        isExpiringSoon: false
      };
    }
    
    const isExpiringSoon = isTokenExpiringSoon(tokenData.expiresAt);
    
    return {
      hasToken: !!tokenData.accessToken,
      isValid: !!tokenData.accessToken && !isExpiringSoon,
      expiresAt: tokenData.expiresAt,
      isExpiringSoon,
      scopes: tokenData.scopes,
      subscriptionType: tokenData.subscriptionType
    };
  }

  /**
   * 启用自动刷新
   * @param {number} interval - 检查间隔（毫秒）
   */
  enableAutoRefresh(interval = 60000) {
    this.autoRefreshEnabled = true;
    
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
    
    this.refreshInterval = setInterval(async () => {
      try {
        await this.refreshTokenIfNeeded();
      } catch (error) {
        console.error("Auto refresh failed:", error);
      }
    }, interval);
  }

  /**
   * 禁用自动刷新
   */
  disableAutoRefresh() {
    this.autoRefreshEnabled = false;
    
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
    }
  }

  /**
   * 验证API密钥
   * @param {string} apiKey - API密钥
   * @returns {boolean} 是否有效
   */
  validateAPIKey(apiKey) {
    return isAPIKeyApproved(apiKey);
  }

  /**
   * 批准API密钥
   * @param {string} apiKey - API密钥
   */
  approveAPIKey(apiKey) {
    const userConfig = getUserConfig();
    
    if (!userConfig.customApiKeyResponses) {
      userConfig.customApiKeyResponses = {
        approved: [],
        rejected: []
      };
    }
    
    if (!userConfig.customApiKeyResponses.approved) {
      userConfig.customApiKeyResponses.approved = [];
    }
    
    const keyHash = hashAPIKey(apiKey);
    if (!userConfig.customApiKeyResponses.approved.includes(keyHash)) {
      userConfig.customApiKeyResponses.approved.push(keyHash);
    }
    
    saveUserConfig(userConfig);
    clearAPIKeyCache();
  }

  /**
   * 获取订阅信息
   * @returns {Object} 订阅信息
   */
  getSubscriptionInfo() {
    return {
      type: getCurrentSubscriptionType(),
      displayName: getSubscriptionDisplayName(),
      isPaid: isPaidUser(),
      shouldUseClaudeAI: shouldUseClaudeAI()
    };
  }
}

// 辅助函数

/**
 * 获取用户配置
 * @returns {Object} 用户配置
 */
function getUserConfig() {
  // @todo: 实现E0函数的实际逻辑
  return {};
}

/**
 * 保存用户配置
 * @param {Object} config - 用户配置
 */
function saveUserConfig(config) {
  // @todo: 实现pA函数的实际逻辑
}

/**
 * 哈希API密钥
 * @param {string} apiKey - API密钥
 * @returns {string} 哈希值
 */
function hashAPIKey(apiKey) {
  // @todo: 实现CK函数的实际逻辑
  const crypto = require('crypto');
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

/**
 * 清除API密钥缓存
 */
function clearAPIKeyCache() {
  // @todo: 实现v41.cache.clear的实际逻辑
}

/**
 * 检查作用域是否有效
 * @param {Array} scopes - 作用域数组
 * @returns {boolean} 是否有效
 */
function hasValidScopes(scopes) {
  // @todo: 实现hy函数的实际逻辑
  return Array.isArray(scopes) && scopes.length > 0;
}

/**
 * 获取配置管理器
 * @returns {Object} 配置管理器
 */
function getConfigManager() {
  // @todo: 实现tC函数的实际逻辑
  return {
    read: () => ({}),
    update: (config) => ({ success: true })
  };
}

/**
 * 清除令牌数据缓存
 */
function clearTokenDataCache() {
  // @todo: 实现FD.cache?.clear的实际逻辑
}

/**
 * 清除工作空间缓存
 */
function clearWorkspaceCache() {
  // @todo: 实现wV.cache?.clear的实际逻辑
}

/**
 * 获取存储的令牌数据
 * @returns {Object|null} 令牌数据
 */
function getStoredTokenData() {
  // @todo: 实现FD函数的实际逻辑
  return null;
}

/**
 * 检查令牌是否即将过期
 * @param {number} expiresAt - 过期时间戳
 * @returns {boolean} 是否即将过期
 */
function isTokenExpiringSoon(expiresAt) {
  const { isTokenExpiringSoon } = require('./credentials-service.js');
  return isTokenExpiringSoon(expiresAt);
}

/**
 * 获取锁目录
 * @returns {string} 锁目录路径
 */
function getLockDirectory() {
  // @todo: 实现YQ函数的实际逻辑
  const { join } = require('path');
  const { tmpdir } = require('os');
  return join(tmpdir(), 'claude-cli-locks');
}

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 */
function getFileSystem() {
  return require('fs');
}

/**
 * 获取锁
 * @param {string} lockDir - 锁目录
 * @returns {Promise<Function>} 释放锁的函数
 */
async function acquireLock(lockDir) {
  // @todo: 实现taA.lock的实际逻辑
  const lockfile = require('proper-lockfile');
  return lockfile.lock(lockDir);
}

/**
 * 获取Keychain服务名称
 * @returns {string} 服务名称
 */
function getKeychainServiceName() {
  // @todo: 实现p91函数的实际逻辑
  return "claude-cli";
}

/**
 * 执行命令
 * @param {string} command - 命令
 * @returns {string} 命令输出
 */
function executeCommand(command) {
  // @todo: 实现GD函数的实际逻辑
  const { execSync } = require('child_process');
  return execSync(command, { encoding: 'utf8' });
}

/**
 * 记录错误
 * @param {Error} error - 错误对象
 */
function logError(error) {
  console.error('Token service error:', error);
}

/**
 * 检查是否为企业版本
 * @returns {boolean} 是否为企业版本
 */
function isEnterpriseVersion() {
  // @todo: 实现F9函数的实际逻辑
  return false;
}

/**
 * 检查是否有有效订阅
 * @returns {boolean} 是否有有效订阅
 */
function hasValidSubscription() {
  // @todo: 实现mY函数的实际逻辑
  return false;
}

/**
 * 获取当前订阅类型
 * @returns {string} 订阅类型
 */
function getCurrentSubscriptionType() {
  // @todo: 实现FsA函数的实际逻辑
  return "free";
}

// 创建默认令牌服务实例
export const tokenService = new TokenService();
