/**
 * 权限管理服务
 * @description 重构自原始文件中的权限管理代码，对应第3005-3090行
 * @original 原始代码行 3005-3090
 */

import { parsePermissionRules, getBlockedTools, getBlockedIgnorePatterns } from './file-read-service.js';

/**
 * 权限源列表
 * @original zw变量
 */
const PERMISSION_SOURCES = ["localSettings", "projectSettings"];

/**
 * 迁移项目设置到权限系统
 * @description 将旧的项目设置格式迁移到新的权限系统
 * @original CIA函数
 */
export function migrateProjectSettings() {
  const projectConfig = getProjectConfig();
  
  if (!projectConfig.allowedTools && !projectConfig.ignorePatterns) {
    return;
  }
  
  // 创建配置副本
  const configCopy = { ...projectConfig };
  
  // 处理被阻止的工具
  const blockedTools = getBlockedTools(projectConfig, getPermissionRules("localSettings"));
  if (blockedTools.length > 0) {
    addPermissionRules({
      ruleValues: blockedTools.map(normalizeRule),
      ruleBehavior: "allow"
    }, "localSettings");
  }
  
  // 清空允许的工具列表
  configCopy.allowedTools = [];
  
  // 处理被阻止的忽略模式
  const blockedPatterns = getBlockedIgnorePatterns(projectConfig, getPermissionRules("localSettings"));
  if (blockedPatterns.length > 0) {
    addPermissionRules({
      ruleValues: blockedPatterns,
      ruleBehavior: "deny"
    }, "localSettings");
  }
  
  // 删除忽略模式并保存配置
  delete configCopy.ignorePatterns;
  saveProjectConfig(configCopy);
}

/**
 * 获取所有权限规则
 * @returns {Array} 权限规则数组
 * @original IK1函数
 */
export function getAllPermissionRules() {
  const rules = [];
  const projectConfig = getProjectConfig();
  
  // 添加项目设置中的允许工具规则
  for (const tool of projectConfig.allowedTools || []) {
    rules.push({
      source: "projectSettings",
      ruleBehavior: "allow",
      ruleValue: normalizeRule(tool)
    });
  }
  
  // 添加其他源的权限规则
  for (const source of PERMISSION_SOURCES) {
    rules.push(...getPermissionRules(source));
  }
  
  return rules;
}

/**
 * 获取指定源的权限规则
 * @param {string} source - 权限源
 * @returns {Array} 权限规则数组
 * @original Ir1函数
 */
export function getPermissionRules(source) {
  const config = getConfigBySource(source);
  return parsePermissionRules(config, source);
}

/**
 * 删除权限规则
 * @param {Object} rule - 要删除的权限规则
 * @returns {boolean} 是否删除成功
 * @original KIA函数
 */
export function removePermissionRule(rule) {
  const toolName = getToolName(rule.ruleValue);
  const config = getConfigBySource(rule.source);
  
  if (!config || !config.permissions) {
    return false;
  }
  
  const behaviorRules = config.permissions[rule.ruleBehavior];
  if (!behaviorRules || !behaviorRules.includes(toolName)) {
    return false;
  }
  
  try {
    const updatedConfig = {
      ...config,
      permissions: {
        ...config.permissions,
        [rule.ruleBehavior]: behaviorRules.filter(tool => tool !== toolName)
      }
    };
    
    const { error } = saveConfigBySource(rule.source, updatedConfig);
    if (error) {
      return false;
    }
    
    return true;
  } catch (error) {
    logError(error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

/**
 * 创建默认权限配置
 * @returns {Object} 默认权限配置
 * @original D8Q函数
 */
export function createDefaultPermissionConfig() {
  return {
    permissions: {
      allow: [],
      deny: []
    }
  };
}

/**
 * 添加权限规则
 * @param {Object} ruleSet - 规则集合
 * @param {string} source - 权限源
 * @returns {boolean} 是否添加成功
 * @original FK1函数
 */
export function addPermissionRules({ ruleValues, ruleBehavior }, source) {
  if (ruleValues.length < 1) {
    return true;
  }
  
  const toolNames = ruleValues.map(getToolName);
  const config = getConfigBySource(source) || createDefaultPermissionConfig();
  
  try {
    const permissions = config.permissions || {};
    const updatedConfig = {
      ...config,
      permissions: {
        ...permissions,
        [ruleBehavior]: [...(permissions[ruleBehavior] || []), ...toolNames]
      }
    };
    
    saveConfigBySource(source, updatedConfig);
    return true;
  } catch (error) {
    logError(error instanceof Error ? error : new Error(String(error)));
    return false;
  }
}

/**
 * 权限管理服务类
 * @description 提供完整的权限管理功能
 */
export class PermissionService {
  constructor() {
    this.sources = new Set(PERMISSION_SOURCES);
    this.cache = new Map();
  }

  /**
   * 检查工具是否被允许
   * @param {string} toolName - 工具名称
   * @param {string} source - 权限源（可选）
   * @returns {boolean} 是否被允许
   */
  isToolAllowed(toolName, source = null) {
    const rules = source ? this.getPermissionRules(source) : this.getAllPermissionRules();
    
    let allowed = false;
    let denied = false;
    
    for (const rule of rules) {
      const ruleToolName = getToolName(rule.ruleValue);
      if (ruleToolName === toolName) {
        if (rule.ruleBehavior === "allow") {
          allowed = true;
        } else if (rule.ruleBehavior === "deny") {
          denied = true;
        }
      }
    }
    
    // 拒绝规则优先于允许规则
    return allowed && !denied;
  }

  /**
   * 检查路径是否被忽略
   * @param {string} path - 文件路径
   * @param {string} source - 权限源（可选）
   * @returns {boolean} 是否被忽略
   */
  isPathIgnored(path, source = null) {
    const rules = source ? this.getPermissionRules(source) : this.getAllPermissionRules();
    
    for (const rule of rules) {
      if (rule.ruleBehavior === "deny" && 
          rule.ruleValue.ruleContent && 
          this.matchesPattern(path, rule.ruleValue.ruleContent)) {
        return true;
      }
    }
    
    return false;
  }

  /**
   * 模式匹配
   * @param {string} text - 要匹配的文本
   * @param {string} pattern - 模式
   * @returns {boolean} 是否匹配
   * @private
   */
  matchesPattern(text, pattern) {
    // 简单的glob模式匹配
    const regexPattern = pattern
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.')
      .replace(/\[([^\]]+)\]/g, '[$1]');
    
    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(text);
  }

  /**
   * 获取所有权限规则
   * @returns {Array} 权限规则数组
   */
  getAllPermissionRules() {
    return getAllPermissionRules();
  }

  /**
   * 获取指定源的权限规则
   * @param {string} source - 权限源
   * @returns {Array} 权限规则数组
   */
  getPermissionRules(source) {
    return getPermissionRules(source);
  }

  /**
   * 添加权限规则
   * @param {string} toolName - 工具名称
   * @param {string} behavior - 行为 (allow/deny)
   * @param {string} source - 权限源
   * @param {Object} options - 额外选项
   * @returns {boolean} 是否添加成功
   */
  addRule(toolName, behavior, source, options = {}) {
    const ruleValue = {
      toolName,
      ...options
    };
    
    return addPermissionRules({
      ruleValues: [ruleValue],
      ruleBehavior: behavior
    }, source);
  }

  /**
   * 删除权限规则
   * @param {Object} rule - 要删除的规则
   * @returns {boolean} 是否删除成功
   */
  removeRule(rule) {
    return removePermissionRule(rule);
  }

  /**
   * 清空指定源的权限规则
   * @param {string} source - 权限源
   * @param {string} behavior - 行为 (allow/deny)，可选
   * @returns {boolean} 是否清空成功
   */
  clearRules(source, behavior = null) {
    try {
      const config = getConfigBySource(source) || createDefaultPermissionConfig();
      const permissions = config.permissions || {};
      
      if (behavior) {
        permissions[behavior] = [];
      } else {
        permissions.allow = [];
        permissions.deny = [];
      }
      
      const updatedConfig = {
        ...config,
        permissions
      };
      
      saveConfigBySource(source, updatedConfig);
      return true;
    } catch (error) {
      logError(error);
      return false;
    }
  }

  /**
   * 获取权限统计信息
   * @returns {Object} 权限统计信息
   */
  getStats() {
    const allRules = this.getAllPermissionRules();
    const stats = {
      totalRules: allRules.length,
      allowRules: 0,
      denyRules: 0,
      sourceStats: {}
    };
    
    for (const rule of allRules) {
      if (rule.ruleBehavior === "allow") {
        stats.allowRules++;
      } else if (rule.ruleBehavior === "deny") {
        stats.denyRules++;
      }
      
      if (!stats.sourceStats[rule.source]) {
        stats.sourceStats[rule.source] = { allow: 0, deny: 0 };
      }
      stats.sourceStats[rule.source][rule.ruleBehavior]++;
    }
    
    return stats;
  }

  /**
   * 验证权限配置
   * @param {Object} config - 权限配置
   * @returns {Object} 验证结果
   */
  validateConfig(config) {
    const result = {
      valid: true,
      errors: [],
      warnings: []
    };
    
    if (!config) {
      result.valid = false;
      result.errors.push('Configuration is required');
      return result;
    }
    
    if (!config.permissions) {
      result.warnings.push('No permissions section found');
      return result;
    }
    
    const { permissions } = config;
    
    if (!Array.isArray(permissions.allow)) {
      result.errors.push('permissions.allow must be an array');
      result.valid = false;
    }
    
    if (!Array.isArray(permissions.deny)) {
      result.errors.push('permissions.deny must be an array');
      result.valid = false;
    }
    
    return result;
  }

  /**
   * 导出权限配置
   * @param {string} source - 权限源（可选）
   * @returns {Object} 权限配置
   */
  exportConfig(source = null) {
    if (source) {
      return getConfigBySource(source);
    }
    
    const config = {};
    for (const src of this.sources) {
      config[src] = getConfigBySource(src);
    }
    
    return config;
  }

  /**
   * 导入权限配置
   * @param {Object} config - 权限配置
   * @param {string} source - 权限源
   * @returns {boolean} 是否导入成功
   */
  importConfig(config, source) {
    const validation = this.validateConfig(config);
    if (!validation.valid) {
      logError(new Error(`Invalid config: ${validation.errors.join(', ')}`));
      return false;
    }
    
    try {
      saveConfigBySource(source, config);
      return true;
    } catch (error) {
      logError(error);
      return false;
    }
  }
}

// 辅助函数

/**
 * 获取项目配置
 * @returns {Object} 项目配置
 * @original t9函数的实现（推测）
 */
function getProjectConfig() {
  // @todo: 实现t9函数的实际逻辑
  return {
    allowedTools: [],
    ignorePatterns: []
  };
}

/**
 * 保存项目配置
 * @param {Object} config - 项目配置
 * @original S5函数的实现（推测）
 */
function saveProjectConfig(config) {
  // @todo: 实现S5函数的实际逻辑
  console.log('Saving project config:', config);
}

/**
 * 根据源获取配置
 * @param {string} source - 配置源
 * @returns {Object} 配置对象
 * @original $Y函数的实现（推测）
 */
function getConfigBySource(source) {
  // @todo: 实现$Y函数的实际逻辑
  return createDefaultPermissionConfig();
}

/**
 * 根据源保存配置
 * @param {string} source - 配置源
 * @param {Object} config - 配置对象
 * @returns {Object} 保存结果
 * @original f8函数的实现（推测）
 */
function saveConfigBySource(source, config) {
  // @todo: 实现f8函数的实际逻辑
  console.log(`Saving config for ${source}:`, config);
  return { error: null };
}

/**
 * 规范化规则
 * @param {*} rule - 原始规则
 * @returns {*} 规范化后的规则
 * @original Az函数的实现（推测）
 */
function normalizeRule(rule) {
  // @todo: 实现Az函数的实际逻辑
  return rule;
}

/**
 * 获取工具名称
 * @param {*} ruleValue - 规则值
 * @returns {string} 工具名称
 * @original d5函数的实现（推测）
 */
function getToolName(ruleValue) {
  // @todo: 实现d5函数的实际逻辑
  return ruleValue.toolName || ruleValue;
}

/**
 * 记录错误
 * @param {Error} error - 错误对象
 * @original T1函数的实现（推测）
 */
function logError(error) {
  // @todo: 实现T1函数的实际逻辑
  console.error('Permission service error:', error);
}

/**
 * 获取总是拒绝的规则
 * @param {Object} config - 配置对象
 * @returns {Array} 总是拒绝的规则数组
 * @original ji函数
 */
export function getAlwaysDenyRules(config) {
  return PERMISSION_SOURCES_EXTENDED.flatMap(source =>
    (config.alwaysDenyRules[source] || []).map(rule => ({
      source,
      ruleBehavior: "deny",
      ruleValue: parsePermissionRule(rule)
    }))
  );
}

/**
 * 检查工具是否匹配规则
 * @param {Object} tool - 工具对象
 * @param {Object} rule - 规则对象
 * @returns {boolean} 是否匹配
 * @original jIA函数
 */
export function doesToolMatchRule(tool, rule) {
  // 如果规则有具体内容，不匹配
  if (rule.ruleValue.ruleContent !== undefined) {
    return false;
  }

  // 如果工具名称完全匹配
  if (rule.ruleValue.toolName === tool.name) {
    return true;
  }

  // 检查MCP工具匹配
  const ruleToolInfo = parseMCPToolName(rule.ruleValue.toolName);
  const toolInfo = parseMCPToolName(tool.name);

  return ruleToolInfo !== null &&
         toolInfo !== null &&
         ruleToolInfo.toolName === undefined &&
         ruleToolInfo.serverName === toolInfo.serverName;
}

/**
 * 查找工具的允许规则
 * @param {Object} config - 配置对象
 * @param {Object} tool - 工具对象
 * @returns {Object|null} 匹配的规则或null
 * @original E5Q函数
 */
export function findToolAllowRule(config, tool) {
  return getAlwaysAllowRules(config).find(rule => doesToolMatchRule(tool, rule)) || null;
}

/**
 * 查找工具的拒绝规则
 * @param {Object} config - 配置对象
 * @param {Object} tool - 工具对象
 * @returns {Object|null} 匹配的规则或null
 * @original U5Q函数
 */
export function findToolDenyRule(config, tool) {
  return getAlwaysDenyRules(config).find(rule => doesToolMatchRule(tool, rule)) || null;
}

/**
 * 获取工具的内容规则映射
 * @param {Object} config - 配置对象
 * @param {Object} tool - 工具对象
 * @param {string} behavior - 行为类型 (allow/deny)
 * @returns {Map} 内容规则映射
 * @original yi和zr1函数
 */
export function getToolContentRules(config, tool, behavior) {
  return getToolContentRulesByName(config, tool.name, behavior);
}

/**
 * 根据工具名称获取内容规则映射
 * @param {Object} config - 配置对象
 * @param {string} toolName - 工具名称
 * @param {string} behavior - 行为类型 (allow/deny)
 * @returns {Map} 内容规则映射
 * @original zr1函数
 */
export function getToolContentRulesByName(config, toolName, behavior) {
  const ruleMap = new Map();
  let rules = [];

  switch (behavior) {
    case "allow":
      rules = getAlwaysAllowRules(config);
      break;
    case "deny":
      rules = getAlwaysDenyRules(config);
      break;
  }

  for (const rule of rules) {
    if (rule.ruleValue.toolName === toolName &&
        rule.ruleValue.ruleContent !== undefined &&
        rule.ruleBehavior === behavior) {
      ruleMap.set(rule.ruleValue.ruleContent, rule);
    }
  }

  return ruleMap;
}

/**
 * 工具权限检查器
 * @param {Object} tool - 工具对象
 * @param {Object} input - 输入参数
 * @param {Object} context - 执行上下文
 * @returns {Promise<Object>} 权限检查结果
 * @original Nw函数
 */
export async function checkToolPermissions(tool, input, context) {
  // 检查是否已中止
  if (context.abortController.signal.aborted) {
    throw new AbortError();
  }

  // 检查拒绝规则
  const denyRule = findToolDenyRule(context.getToolPermissionContext(), tool);
  if (denyRule) {
    return {
      behavior: "deny",
      decisionReason: {
        type: "rule",
        rule: denyRule
      },
      ruleSuggestions: null,
      message: `Permission to use ${tool.name} has been denied.`
    };
  }

  // 检查工具特定权限
  let toolPermissionResult;
  try {
    const parsedInput = tool.inputSchema.parse(input);
    toolPermissionResult = await tool.checkPermissions(parsedInput, context);
  } catch (error) {
    logError(error);
    return {
      behavior: "ask",
      message: "Error checking permissions"
    };
  }

  // 如果工具拒绝，直接返回
  if (toolPermissionResult?.behavior === "deny") {
    return toolPermissionResult;
  }

  // 检查是否绕过权限
  if (context.getToolPermissionContext().mode === "bypassPermissions") {
    return {
      behavior: "allow",
      updatedInput: input,
      decisionReason: {
        type: "mode",
        mode: context.getToolPermissionContext().mode
      }
    };
  }

  // 检查允许规则
  const allowRule = findToolAllowRule(context.getToolPermissionContext(), tool);
  if (allowRule) {
    return {
      behavior: "allow",
      updatedInput: input,
      decisionReason: {
        type: "rule",
        rule: allowRule
      }
    };
  }

  // 如果工具允许，直接返回
  if (toolPermissionResult.behavior === "allow") {
    return toolPermissionResult;
  }

  // 如果是passthrough，转换为ask
  if (toolPermissionResult.behavior === "passthrough") {
    return {
      ...toolPermissionResult,
      behavior: "ask",
      message: generatePermissionRequestMessage(context.getToolPermissionContext(), tool.name)
    };
  }

  return toolPermissionResult;
}

/**
 * 获取规则属性名称
 * @param {string} behavior - 行为类型
 * @returns {string} 规则属性名称
 * @original yIA函数
 */
export function getRulePropertyName(behavior) {
  switch (behavior) {
    case "allow":
      return "alwaysAllowRules";
    case "deny":
      return "alwaysDenyRules";
    default:
      throw new Error(`Unknown behavior: ${behavior}`);
  }
}

/**
 * 保存单个权限规则
 * @param {Object} options - 保存选项
 * @returns {Promise<boolean>} 是否保存成功
 * @original wK1函数
 */
export async function saveSinglePermissionRule(options) {
  return savePermissionRules({
    ...options,
    ruleValues: [options.rule.ruleValue],
    ruleBehavior: options.rule.ruleBehavior,
    destination: options.rule.source
  });
}

/**
 * 保存权限规则
 * @param {Object} options - 保存选项
 * @returns {Promise<boolean>} 是否保存成功
 * @original R91函数的完整实现
 */
export async function savePermissionRules(options) {
  const {
    ruleBehavior,
    destination,
    initialContext,
    setToolPermissionContext,
    ruleValues
  } = options;

  // 创建规则集合，避免重复
  const ruleSet = new Set(ruleValues.map(formatPermissionRule));
  const rulePropertyName = getRulePropertyName(ruleBehavior);

  // 更新上下文
  const updatedContext = {
    ...initialContext,
    [rulePropertyName]: {
      ...initialContext[rulePropertyName],
      [destination]: [
        ...(initialContext[rulePropertyName][destination] || []),
        ...ruleSet
      ]
    }
  };

  // 保存到配置文件
  addPermissionRules({
    ruleValues,
    ruleBehavior
  }, destination);

  // 更新上下文
  setToolPermissionContext(updatedContext);

  return true;
}

/**
 * 删除权限规则
 * @param {Object} options - 删除选项
 * @returns {Promise<boolean>} 是否删除成功
 * @original kIA函数
 */
export async function deletePermissionRule(options) {
  const {
    rule,
    initialContext,
    setToolPermissionContext
  } = options;

  // 检查是否为受管理的设置
  if (rule.source === "policySettings") {
    throw new Error("Cannot delete permission rules from managed settings");
  }

  const ruleString = formatPermissionRule(rule.ruleValue);
  const rulePropertyName = getRulePropertyName(rule.ruleBehavior);
  const source = rule.source;

  // 更新上下文
  const updatedContext = {
    ...initialContext,
    [rulePropertyName]: {
      ...initialContext[rulePropertyName],
      [rule.source]: initialContext[rulePropertyName][source]?.filter(r => r !== ruleString) || []
    }
  };

  // 根据源类型处理删除
  switch (source) {
    case "localSettings":
    case "userSettings":
    case "projectSettings":
      {
        // 从配置文件中删除
        removePermissionRule(rule);
        break;
      }
    case "cliArg":
    case "command":
    case "flagSettings":
      // 这些源不需要持久化删除
      break;
  }

  // 更新上下文
  setToolPermissionContext(updatedContext);

  return true;
}

/**
 * 构建权限上下文
 * @param {Object} baseContext - 基础上下文
 * @param {Array} rules - 权限规则数组
 * @returns {Object} 构建后的权限上下文
 * @original $K1函数
 */
export function buildPermissionContext(baseContext, rules) {
  const alwaysAllowRules = { ...baseContext.alwaysAllowRules };
  const alwaysDenyRules = { ...baseContext.alwaysDenyRules };

  for (const rule of rules) {
    const ruleString = formatPermissionRule(rule.ruleValue);
    const source = rule.source;

    const targetRules = (() => {
      switch (rule.ruleBehavior) {
        case "allow":
          return alwaysAllowRules;
        case "deny":
          return alwaysDenyRules;
        default:
          throw new Error(`Unknown rule behavior: ${rule.ruleBehavior}`);
      }
    })();

    if (!targetRules[source]) {
      targetRules[source] = [];
    }

    if (targetRules[source]) {
      targetRules[source].push(ruleString);
    }
  }

  return {
    ...baseContext,
    alwaysAllowRules,
    alwaysDenyRules
  };
}

// 扩展PermissionService类
export class ExtendedPermissionService extends PermissionService {
  /**
   * 检查工具权限
   * @param {Object} tool - 工具对象
   * @param {Object} input - 输入参数
   * @param {Object} context - 执行上下文
   * @returns {Promise<Object>} 权限检查结果
   */
  async checkToolPermissions(tool, input, context) {
    return checkToolPermissions(tool, input, context);
  }

  /**
   * 查找工具的允许规则
   * @param {Object} config - 配置对象
   * @param {Object} tool - 工具对象
   * @returns {Object|null} 匹配的规则或null
   */
  findToolAllowRule(config, tool) {
    return findToolAllowRule(config, tool);
  }

  /**
   * 查找工具的拒绝规则
   * @param {Object} config - 配置对象
   * @param {Object} tool - 工具对象
   * @returns {Object|null} 匹配的规则或null
   */
  findToolDenyRule(config, tool) {
    return findToolDenyRule(config, tool);
  }

  /**
   * 获取工具的内容规则
   * @param {Object} config - 配置对象
   * @param {Object} tool - 工具对象
   * @param {string} behavior - 行为类型
   * @returns {Map} 内容规则映射
   */
  getToolContentRules(config, tool, behavior) {
    return getToolContentRules(config, tool, behavior);
  }

  /**
   * 保存权限规则
   * @param {Object} options - 保存选项
   * @returns {Promise<boolean>} 是否保存成功
   */
  async savePermissionRules(options) {
    return savePermissionRules(options);
  }

  /**
   * 保存单个权限规则
   * @param {Object} options - 保存选项
   * @returns {Promise<boolean>} 是否保存成功
   */
  async saveSinglePermissionRule(options) {
    return saveSinglePermissionRule(options);
  }

  /**
   * 获取拒绝规则
   * @param {Object} config - 配置对象
   * @returns {Array} 拒绝规则数组
   */
  getAlwaysDenyRules(config) {
    return getAlwaysDenyRules(config);
  }

  /**
   * 检查工具是否匹配规则
   * @param {Object} tool - 工具对象
   * @param {Object} rule - 规则对象
   * @returns {boolean} 是否匹配
   */
  doesToolMatchRule(tool, rule) {
    return doesToolMatchRule(tool, rule);
  }

  /**
   * 删除权限规则
   * @param {Object} options - 删除选项
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deletePermissionRule(options) {
    return deletePermissionRule(options);
  }

  /**
   * 构建权限上下文
   * @param {Object} baseContext - 基础上下文
   * @param {Array} rules - 权限规则数组
   * @returns {Object} 构建后的权限上下文
   */
  buildPermissionContext(baseContext, rules) {
    return buildPermissionContext(baseContext, rules);
  }

  /**
   * 获取规则属性名称
   * @param {string} behavior - 行为类型
   * @returns {string} 规则属性名称
   */
  getRulePropertyName(behavior) {
    return getRulePropertyName(behavior);
  }

  /**
   * 解析权限规则字符串
   * @param {string} rule - 规则字符串
   * @returns {Object} 解析后的规则对象
   */
  parsePermissionRule(rule) {
    return parsePermissionRule(rule);
  }

  /**
   * 格式化权限规则对象
   * @param {Object} rule - 规则对象
   * @returns {string} 格式化后的规则字符串
   */
  formatPermissionRule(rule) {
    return formatPermissionRule(rule);
  }

  /**
   * 批量处理权限规则
   * @param {Array} rules - 规则数组
   * @param {string} operation - 操作类型 (add/remove)
   * @param {Object} context - 上下文对象
   * @returns {Promise<Object>} 处理结果
   */
  async batchProcessRules(rules, operation, context) {
    const results = {
      successful: [],
      failed: [],
      totalProcessed: 0
    };

    for (const rule of rules) {
      try {
        let success = false;

        switch (operation) {
          case 'add':
            success = await this.savePermissionRules({
              ruleValues: [rule.ruleValue],
              ruleBehavior: rule.ruleBehavior,
              destination: rule.source,
              initialContext: context.initialContext,
              setToolPermissionContext: context.setToolPermissionContext
            });
            break;
          case 'remove':
            success = await this.deletePermissionRule({
              rule,
              initialContext: context.initialContext,
              setToolPermissionContext: context.setToolPermissionContext
            });
            break;
          default:
            throw new Error(`Unknown operation: ${operation}`);
        }

        if (success) {
          results.successful.push(rule);
        } else {
          results.failed.push({ rule, error: 'Operation failed' });
        }
      } catch (error) {
        results.failed.push({ rule, error: error.message });
      }

      results.totalProcessed++;
    }

    return results;
  }

  /**
   * 验证权限规则
   * @param {Object} rule - 权限规则
   * @returns {Object} 验证结果
   */
  validatePermissionRule(rule) {
    const result = {
      valid: true,
      errors: [],
      warnings: []
    };

    if (!rule) {
      result.valid = false;
      result.errors.push('Rule is required');
      return result;
    }

    if (!rule.ruleValue) {
      result.valid = false;
      result.errors.push('Rule value is required');
    }

    if (!rule.ruleBehavior || !['allow', 'deny'].includes(rule.ruleBehavior)) {
      result.valid = false;
      result.errors.push('Rule behavior must be "allow" or "deny"');
    }

    if (!rule.source) {
      result.valid = false;
      result.errors.push('Rule source is required');
    }

    if (rule.ruleValue && !rule.ruleValue.toolName) {
      result.valid = false;
      result.errors.push('Tool name is required in rule value');
    }

    return result;
  }

  /**
   * 获取权限规则冲突
   * @param {Array} rules - 权限规则数组
   * @returns {Array} 冲突规则数组
   */
  findRuleConflicts(rules) {
    const conflicts = [];
    const ruleMap = new Map();

    for (const rule of rules) {
      const key = `${rule.ruleValue.toolName}:${rule.ruleValue.ruleContent || ''}`;

      if (ruleMap.has(key)) {
        const existingRule = ruleMap.get(key);
        if (existingRule.ruleBehavior !== rule.ruleBehavior) {
          conflicts.push({
            type: 'behavior_conflict',
            rules: [existingRule, rule],
            description: `Conflicting behaviors for ${rule.ruleValue.toolName}`
          });
        }
      } else {
        ruleMap.set(key, rule);
      }
    }

    return conflicts;
  }

  /**
   * 优化权限规则
   * @param {Array} rules - 权限规则数组
   * @returns {Array} 优化后的规则数组
   */
  optimizeRules(rules) {
    const optimized = [];
    const seen = new Set();

    // 去重
    for (const rule of rules) {
      const key = `${rule.source}:${rule.ruleBehavior}:${formatPermissionRule(rule.ruleValue)}`;
      if (!seen.has(key)) {
        seen.add(key);
        optimized.push(rule);
      }
    }

    // 按优先级排序（deny规则优先）
    return optimized.sort((a, b) => {
      if (a.ruleBehavior === 'deny' && b.ruleBehavior === 'allow') return -1;
      if (a.ruleBehavior === 'allow' && b.ruleBehavior === 'deny') return 1;
      return 0;
    });
  }
}

// 辅助函数

/**
 * 解析MCP工具名称
 * @param {string} toolName - 工具名称
 * @returns {Object|null} 解析结果或null
 * @original xy函数的实现（推测）
 */
function parseMCPToolName(toolName) {
  // @todo: 实现xy函数的实际逻辑
  const parts = toolName.split("__");
  const [prefix, serverName, ...toolNameParts] = parts;

  if (prefix !== "mcp" || !serverName) {
    return null;
  }

  const actualToolName = toolNameParts.length > 0 ? toolNameParts.join("__") : undefined;

  return {
    serverName,
    toolName: actualToolName
  };
}

/**
 * 解析权限规则
 * @param {string} rule - 规则字符串
 * @returns {Object} 解析后的规则对象
 * @original Az函数的实现（推测）
 */
function parsePermissionRule(rule) {
  // @todo: 实现Az函数的实际逻辑
  const match = rule.match(/^([^(]+)\(([^)]+)\)$/);

  if (!match) {
    return {
      toolName: rule
    };
  }

  const [, toolName, ruleContent] = match;

  if (!toolName || !ruleContent) {
    return {
      toolName: rule
    };
  }

  return {
    toolName,
    ruleContent
  };
}

/**
 * 格式化权限规则
 * @param {Object} rule - 规则对象
 * @returns {string} 格式化后的规则字符串
 * @original d5函数的实现（推测）
 */
function formatPermissionRule(rule) {
  // @todo: 实现d5函数的实际逻辑
  return rule.ruleContent ? `${rule.toolName}(${rule.ruleContent})` : rule.toolName;
}

/**
 * 生成权限请求消息
 * @param {Object} config - 配置对象
 * @param {string} toolName - 工具名称
 * @returns {string} 权限请求消息
 * @original z5Q函数的实现（推测）
 */
function generatePermissionRequestMessage(config, toolName) {
  // @todo: 实现z5Q函数的实际逻辑
  return `Claude requested permissions to use ${toolName}, but you haven't granted it yet.`;
}

/**
 * 获取总是允许的规则
 * @param {Object} config - 配置对象
 * @returns {Array} 总是允许的规则数组
 * @original Wh函数的实现（推测）
 */
function getAlwaysAllowRules(config) {
  // @todo: 实现Wh函数的实际逻辑
  return PERMISSION_SOURCES_EXTENDED.flatMap(source =>
    (config.alwaysAllowRules?.[source] || []).map(rule => ({
      source,
      ruleBehavior: "allow",
      ruleValue: parsePermissionRule(rule)
    }))
  );
}

/**
 * 中止错误类
 * @original nJ类的实现（推测）
 */
class AbortError extends Error {
  constructor(message = 'Operation was aborted') {
    super(message);
    this.name = 'AbortError';
  }
}

// 创建默认权限服务实例
export const permissionService = new ExtendedPermissionService();
