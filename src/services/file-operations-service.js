/**
 * 文件操作服务
 * @description 重构自原始文件中的文件操作代码，对应第3716-3800行
 * @original 原始代码行 3716-3800
 */

import { createAbortController } from '../utils/abort-controller-polyfill.js';
import { getIgnorePatternMap, buildPatternList } from '../utils/pattern-matching-utils.js';

/**
 * 搜索文件
 * @param {string} pattern - 搜索模式
 * @param {string} cwd - 当前工作目录
 * @param {Object} options - 搜索选项
 * @param {AbortSignal} signal - 中止信号
 * @param {Object} context - 上下文对象
 * @returns {Promise<Object>} 搜索结果
 * @original uIA函数
 */
export async function searchFiles(pattern, cwd, { limit, offset }, signal, context) {
  const ignorePatterns = buildPatternList(getIgnorePatternMap(context), cwd);
  
  // 使用glob搜索文件
  const files = (await globSearch([pattern], {
    cwd,
    nocase: true,
    nodir: true,
    signal,
    stat: true,
    withFileTypes: true,
    ignore: ignorePatterns
  })).sort((a, b) => (a.mtimeMs ?? 0) - (b.mtimeMs ?? 0));
  
  const hasMore = files.length > offset + limit;
  
  return {
    files: files.slice(offset, offset + limit).map(file => file.fullpath()),
    truncated: hasMore
  };
}

/**
 * 读取文件内容（分页）
 * @param {string} filePath - 文件路径
 * @param {number} offset - 起始行号
 * @param {number} limit - 行数限制
 * @returns {Object} 文件内容信息
 * @original mIA函数
 */
export function readFileContent(filePath, offset = 0, limit) {
  const fs = getFileSystem();
  const content = fs.readFileSync(filePath, { encoding: "utf8" });
  const lines = content.split(/\r?\n/);
  
  const selectedLines = limit !== undefined && lines.length - offset > limit ? 
    lines.slice(offset, offset + limit) : 
    lines.slice(offset);
  
  return {
    content: selectedLines.join('\n'),
    lineCount: selectedLines.length,
    totalLines: lines.length
  };
}

/**
 * 写入文件内容
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @param {string} encoding - 文件编码
 * @param {string} lineEnding - 换行符类型
 * @original by函数
 */
export function writeFileContent(filePath, content, encoding, lineEnding) {
  let processedContent = content;
  
  if (lineEnding === "CRLF") {
    processedContent = content.split('\n').join('\r\n');
  }
  
  writeFileSync(filePath, processedContent, { encoding });
}

/**
 * 检测项目默认换行符
 * @returns {Promise<string>} 换行符类型 (LF/CRLF)
 * @original dIA函数
 */
export const detectProjectLineEnding = createLazyLoader(async () => {
  const abortController = createAbortController();
  
  // 设置1秒超时
  setTimeout(() => {
    abortController.abort();
  }, 1000);
  
  const files = await searchProjectFiles(getCurrentWorkingDirectory(), abortController.signal, 15);
  let crlfCount = 0;
  
  for (const file of files) {
    if (detectFileLineEnding(file) === "CRLF") {
      crlfCount++;
    }
  }
  
  return crlfCount > 3 ? "CRLF" : "LF";
});

/**
 * 检测文件换行符
 * @param {string} filePath - 文件路径
 * @param {string} encoding - 文件编码
 * @returns {string} 换行符类型 (LF/CRLF)
 * @original fO函数
 */
export function detectFileLineEnding(filePath, encoding = "utf8") {
  try {
    const fs = getFileSystem();
    const { resolvedPath } = resolveFilePath(fs, filePath);
    const { buffer, bytesRead } = fs.readSync(resolvedPath, { length: 4096 });
    const content = buffer.toString(encoding, 0, bytesRead);
    
    return analyzeLineEndings(content);
  } catch (error) {
    logError(error);
    return "LF";
  }
}

/**
 * 分析文本中的换行符
 * @param {string} content - 文本内容
 * @returns {string} 换行符类型 (LF/CRLF)
 * @original O5Q函数
 */
export function analyzeLineEndings(content) {
  let crlfCount = 0;
  let lfCount = 0;
  
  for (let i = 0; i < content.length; i++) {
    if (content[i] === '\n') {
      if (i > 0 && content[i - 1] === '\r') {
        crlfCount++;
      } else {
        lfCount++;
      }
    }
  }
  
  return crlfCount > lfCount ? "CRLF" : "LF";
}

/**
 * 解析截图文件路径
 * @param {string} filePath - 文件路径
 * @returns {string} 解析后的文件路径
 * @original Vh函数
 */
export function resolveScreenshotPath(filePath) {
  const resolvedPath = isAbsolutePath(filePath) ? filePath : joinPath(getCurrentWorkingDirectory(), filePath);
  const fs = getFileSystem();
  
  // 特殊字符处理
  const specialChar = String.fromCharCode(8239);
  const screenshotPattern = /^(.+)([ \u202F])(AM|PM)(\.png)$/;
  const basename = getBasename(resolvedPath);
  const match = basename.match(screenshotPattern);
  
  if (match) {
    // 如果文件存在，直接返回
    if (fs.existsSync(resolvedPath)) {
      return resolvedPath;
    }
    
    // 尝试替换特殊字符
    const [, prefix, separator, ampm, extension] = match;
    const alternatives = [
      // 尝试不同的分隔符
      `${prefix} ${ampm}${extension}`,
      `${prefix}${specialChar}${ampm}${extension}`,
      `${prefix}\u202F${ampm}${extension}`
    ];
    
    const directory = getDirname(resolvedPath);
    for (const alternative of alternatives) {
      const alternativePath = joinPath(directory, alternative);
      if (fs.existsSync(alternativePath)) {
        return alternativePath;
      }
    }
  }
  
  return resolvedPath;
}

/**
 * 文件操作服务类
 * @description 提供完整的文件操作功能
 */
export class FileOperationsService {
  constructor() {
    this.defaultEncoding = 'utf8';
    this.defaultLineEnding = 'LF';
    this.readCache = new Map();
    this.cacheTimeout = 30000; // 30秒缓存
  }

  /**
   * 搜索文件
   * @param {string} pattern - 搜索模式
   * @param {Object} options - 搜索选项
   * @returns {Promise<Object>} 搜索结果
   */
  async searchFiles(pattern, options = {}) {
    const {
      cwd = getCurrentWorkingDirectory(),
      limit = 100,
      offset = 0,
      signal,
      context = {}
    } = options;
    
    return searchFiles(pattern, cwd, { limit, offset }, signal, context);
  }

  /**
   * 读取文件内容
   * @param {string} filePath - 文件路径
   * @param {Object} options - 读取选项
   * @returns {Object} 文件内容信息
   */
  readFile(filePath, options = {}) {
    const {
      offset = 0,
      limit,
      useCache = true
    } = options;
    
    const cacheKey = `${filePath}:${offset}:${limit}`;
    
    if (useCache) {
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }
    }
    
    const result = readFileContent(filePath, offset, limit);
    
    if (useCache) {
      this.setCache(cacheKey, result);
    }
    
    return result;
  }

  /**
   * 写入文件内容
   * @param {string} filePath - 文件路径
   * @param {string} content - 文件内容
   * @param {Object} options - 写入选项
   */
  writeFile(filePath, content, options = {}) {
    const {
      encoding = this.defaultEncoding,
      lineEnding = this.defaultLineEnding
    } = options;
    
    writeFileContent(filePath, content, encoding, lineEnding);
    
    // 清除相关缓存
    this.clearFileCache(filePath);
  }

  /**
   * 检测文件换行符
   * @param {string} filePath - 文件路径
   * @param {Object} options - 检测选项
   * @returns {string} 换行符类型
   */
  detectLineEnding(filePath, options = {}) {
    const { encoding = this.defaultEncoding } = options;
    return detectFileLineEnding(filePath, encoding);
  }

  /**
   * 获取项目默认换行符
   * @returns {Promise<string>} 换行符类型
   */
  async getProjectLineEnding() {
    return detectProjectLineEnding();
  }

  /**
   * 解析文件路径
   * @param {string} filePath - 文件路径
   * @returns {string} 解析后的路径
   */
  resolvePath(filePath) {
    return resolveScreenshotPath(filePath);
  }

  /**
   * 批量读取文件
   * @param {Array} filePaths - 文件路径数组
   * @param {Object} options - 读取选项
   * @returns {Promise<Array>} 读取结果数组
   */
  async batchReadFiles(filePaths, options = {}) {
    const results = [];
    
    for (const filePath of filePaths) {
      try {
        const content = this.readFile(filePath, options);
        results.push({
          filePath,
          success: true,
          content
        });
      } catch (error) {
        results.push({
          filePath,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 批量写入文件
   * @param {Array} files - 文件信息数组
   * @param {Object} options - 写入选项
   * @returns {Array} 写入结果数组
   */
  batchWriteFiles(files, options = {}) {
    const results = [];
    
    for (const file of files) {
      try {
        this.writeFile(file.path, file.content, {
          ...options,
          ...file.options
        });
        results.push({
          filePath: file.path,
          success: true
        });
      } catch (error) {
        results.push({
          filePath: file.path,
          success: false,
          error: error.message
        });
      }
    }
    
    return results;
  }

  /**
   * 从缓存获取结果
   * @param {string} key - 缓存键
   * @returns {Object|null} 缓存的结果或null
   * @private
   */
  getFromCache(key) {
    const cached = this.readCache.get(key);
    if (!cached) {
      return null;
    }
    
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.readCache.delete(key);
      return null;
    }
    
    return cached.result;
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {Object} result - 结果
   * @private
   */
  setCache(key, result) {
    this.readCache.set(key, {
      result,
      timestamp: Date.now()
    });
  }

  /**
   * 清除文件相关缓存
   * @param {string} filePath - 文件路径
   */
  clearFileCache(filePath) {
    for (const key of this.readCache.keys()) {
      if (key.startsWith(filePath + ':')) {
        this.readCache.delete(key);
      }
    }
  }

  /**
   * 清空所有缓存
   */
  clearAllCache() {
    this.readCache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计信息
   */
  getCacheStats() {
    return {
      size: this.readCache.size,
      timeout: this.cacheTimeout,
      entries: Array.from(this.readCache.keys())
    };
  }

  /**
   * 设置默认编码
   * @param {string} encoding - 编码
   */
  setDefaultEncoding(encoding) {
    this.defaultEncoding = encoding;
  }

  /**
   * 设置默认换行符
   * @param {string} lineEnding - 换行符类型
   */
  setDefaultLineEnding(lineEnding) {
    this.defaultLineEnding = lineEnding;
  }
}

// 辅助函数

/**
 * 获取文件系统模块
 * @returns {Object} 文件系统模块
 * @original x1函数的实现（推测）
 */
function getFileSystem() {
  return require('fs');
}

/**
 * 同步写入文件
 * @param {string} filePath - 文件路径
 * @param {string} content - 文件内容
 * @param {Object} options - 写入选项
 * @original kN函数的实现（推测）
 */
function writeFileSync(filePath, content, options) {
  const fs = getFileSystem();
  fs.writeFileSync(filePath, content, options);
}

/**
 * 创建懒加载器
 * @param {Function} fn - 要懒加载的函数
 * @returns {Function} 懒加载函数
 * @original SA函数的实现（推测）
 */
function createLazyLoader(fn) {
  let cached = null;
  let promise = null;
  
  return async (...args) => {
    if (cached !== null) {
      return cached;
    }
    
    if (promise !== null) {
      return promise;
    }
    
    promise = fn(...args);
    cached = await promise;
    return cached;
  };
}

/**
 * 搜索项目文件
 * @param {string} cwd - 当前工作目录
 * @param {AbortSignal} signal - 中止信号
 * @param {number} limit - 限制数量
 * @returns {Promise<Array>} 文件列表
 * @original k6A函数的实现（推测）
 */
async function searchProjectFiles(cwd, signal, limit) {
  // @todo: 实现k6A函数的实际逻辑
  return [];
}

/**
 * Glob搜索
 * @param {Array} patterns - 搜索模式数组
 * @param {Object} options - 搜索选项
 * @returns {Promise<Array>} 搜索结果
 * @original DC1函数的实现（推测）
 */
async function globSearch(patterns, options) {
  // @todo: 实现DC1函数的实际逻辑
  return [];
}

/**
 * 解析文件路径
 * @param {Object} fs - 文件系统对象
 * @param {string} filePath - 文件路径
 * @returns {Object} 解析结果
 * @original WV函数的实现（推测）
 */
function resolveFilePath(fs, filePath) {
  return { resolvedPath: filePath };
}

/**
 * 记录错误
 * @param {Error} error - 错误对象
 * @original T1函数的实现（推测）
 */
function logError(error) {
  console.error('File operations error:', error);
}

/**
 * 检查是否为绝对路径
 * @param {string} path - 路径
 * @returns {boolean} 是否为绝对路径
 * @original T91函数的实现（推测）
 */
function isAbsolutePath(path) {
  return require('path').isAbsolute(path);
}

/**
 * 连接路径
 * @param {string} basePath - 基础路径
 * @param {string} relativePath - 相对路径
 * @returns {string} 连接后的路径
 * @original P91函数的实现（推测）
 */
function joinPath(basePath, relativePath) {
  return require('path').join(basePath, relativePath);
}

/**
 * 获取当前工作目录
 * @returns {string} 当前工作目录
 * @original a0函数的实现（推测）
 */
function getCurrentWorkingDirectory() {
  return process.cwd();
}

/**
 * 获取文件基础名
 * @param {string} filePath - 文件路径
 * @returns {string} 基础名
 * @original Ur1函数的实现（推测）
 */
function getBasename(filePath) {
  return require('path').basename(filePath);
}

/**
 * 获取目录名
 * @param {string} filePath - 文件路径
 * @returns {string} 目录名
 * @original getDirname的实现（推测）
 */
function getDirname(filePath) {
  return require('path').dirname(filePath);
}

// 创建默认文件操作服务实例
export const fileOperationsService = new FileOperationsService();
